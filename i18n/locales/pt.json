{"system": "Gerador de Imagens IA", "helloWorld": "O<PERSON><PERSON>!", "Describe the image you want to generate...": "Descreva a imagem que você quer gerar...", "appTitle": "Criação de Imagens IA", "copyright": "Copyright © {year}, Criação de Imagens IA", "available": "Disponível para novos projetos", "notAvailable": "Não disponível no momento", "blog": "Blog", "copyLink": "Copiar link", "minRead": "MIN LEITURA", "articleLinkCopied": "Link do artigo copiado para a área de transferência", "clickToClose": "Clique em qualquer lugar ou pressione ESC para fechar", "promptDetails": "Detalhes do prompt", "generateWithPrompt": "Gerar com este prompt", "generateWithSettings": "Gerar com estas configurações", "preset": "Predefinição", "style": "<PERSON><PERSON><PERSON>", "resolution": "Resolução", "addImage": "<PERSON><PERSON><PERSON><PERSON>", "modelPreset": "Modelo/Predefinição", "imageDimensions": "<PERSON><PERSON><PERSON><PERSON><PERSON> da <PERSON>", "yourImage": "<PERSON><PERSON>", "generate": "<PERSON><PERSON><PERSON>", "nav.aitool": "Ferramenta IA", "nav.api": "API", "nav.login": "Entrar", "nav.history": "História", "nav.orders": "Pedidos", "3D Render": "Renderização 3D", "Acrylic": "Acrílico", "Anime General": "<PERSON><PERSON>", "Creative": "Criativo", "Dynamic": "Dinâmico", "Fashion": "Moda", "Game Concept": "Conceito de Jogo", "Graphic Design 3D": "Design Gráfico 3D", "Illustration": "Ilustração", "None": "<PERSON><PERSON><PERSON>", "Portrait": "Retrato", "Portrait Cinematic": "Retrato Cinematográfico", "Portrait Fashion": "Retrato de Moda", "Ray Traced": "Rastreamento de Raios", "Stock Photo": "Foto de Stock", "Watercolor": "Aquarela", "AI Image Generator": "Gerador de Imagens IA", "Generate AI images from text prompts with a magical particle transformation effect": "Gere imagens IA a partir de prompts de texto com um efeito mágico de transformação de partículas", "Enter your prompt": "Digite seu prompt", "Generating...": "Gerando...", "Generate Image": "<PERSON><PERSON><PERSON>", "Enter a prompt and click Generate Image to create an AI image": "Digite um prompt e clique em Gerar Imagem para criar uma imagem IA", "Prompt:": "Prompt:", "Download": "Baixar", "How It Works": "Como Funciona", "This AI image generator uses a particle-based transformation effect to visualize the creation process. When you enter a prompt and click 'Generate', the system:": "Este gerador de imagens IA usa um efeito de transformação baseado em partículas para visualizar o processo de criação. Quando você digita um prompt e clica em 'Gerar', o sistema:", "Sends your prompt to an AI image generation API": "Envia seu prompt para uma API de geração de imagens IA", "Creates a particle system with thousands of tiny particles": "Cria um sistema de partículas com milhares de pequenas partículas", "Transforms the random noise particles into the generated image": "Transforma as partículas de ruído aleatório na imagem gerada", "The particles start in a random noise pattern and then smoothly transform into the final image, creating a magical effect that simulates the AI's creative process.": "As partículas começam em um padrão de ruído aleatório e então se transformam suavemente na imagem final, criando um efeito mágico que simula o processo criativo da IA.", "Transform": "Transformar", "Transforming...": "Transformando...", "Initializing particles...": "Inicializando partículas...", "Loading image...": "Carregando imagem...", "Creating particle system...": "Criando siste<PERSON> de partículas...", "Adding event listeners...": "Adicionando ouvintes de eventos...", "Ready!": "Pronto!", "AI Image Particle Effect": "Efeito de Partículas de Imagem IA", "A demonstration of the BaseMagicImage component that transforms particles into AI-generated images": "Uma demonstração do componente BaseMagicImage que transforma partículas em imagens geradas por IA", "Click anywhere or press ESC to close": "Clique em qualquer lugar ou pressione ESC para fechar", "auth.login": "Entrar", "auth.loginDescription": "Entre na sua conta para continuar", "auth.email": "Email", "auth.enterEmail": "Digite seu email", "auth.password": "<PERSON><PERSON>", "auth.enterPassword": "Digite sua senha", "auth.rememberMe": "<PERSON><PERSON><PERSON> de mim", "auth.welcomeBack": "Bem-vindo de volta", "auth.signupFailed": "Cadastro falhou", "auth.signupFailedDescription": "Houve um erro durante o cadastro. Por favor, tente novamente.", "auth.dontHaveAccount": "Não tem uma conta?", "auth.signUp": "Cadastrar-se", "auth.forgotPassword": "Esque<PERSON>u a senha?", "auth.bySigningIn": "Ao entrar, você concorda com nossos", "auth.termsOfService": "Termos de Serviço", "auth.signUpTitle": "Cadastrar-se", "auth.signUpDescription": "Crie uma conta para começar", "auth.name": "Nome", "auth.enterName": "Digite seu nome", "auth.createAccount": "C<PERSON><PERSON> conta", "auth.alreadyHaveAccount": "Já tem uma conta?", "auth.bySigningUp": "Ao se cadastrar, você concorda com nossos", "auth.backToHome": "Voltar ao início", "auth.notVerifyAccount": "Sua conta não está verificada. Por favor, verifique sua conta para continuar", "auth.verifyAccount": "Verificar conta", "auth.resendActivationEmail": "Reenviar email de ativação", "auth.accountRecovery": "Recuperação de Conta", "auth.accountRecoveryTitle": "Recupere sua conta", "auth.accountRecoveryDescription": "Digite seu email para receber instruções de redefinição de senha", "auth.sendRecoveryEmail": "Enviar email de recuperação", "auth.recoveryEmailSent": "Email de recuperação enviado", "auth.recoveryEmailSentDescription": "Por favor, verifique seu email para instruções de redefinição de senha", "auth.resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "auth.resetPasswordTitle": "Redefina sua senha", "auth.resetPasswordDescription": "Digite sua nova senha", "auth.newPassword": "Nova senha", "auth.confirmPassword": "Confirmar <PERSON><PERSON><PERSON>", "auth.enterNewPassword": "Digite sua nova senha", "auth.enterConfirmPassword": "Confirme sua nova senha", "auth.passwordResetSuccess": "Redefinição de senha bem-sucedida", "auth.passwordResetSuccessDescription": "Sua senha foi redefinida com sucesso. Agora você pode entrar com sua nova senha", "auth.activateAccount": "Ativar Conta", "auth.activateAccountTitle": "Ative sua conta", "auth.activateAccountDescription": "Sua conta está sendo ativada...", "auth.accountActivated": "Conta ativada", "auth.accountActivatedDescription": "Sua conta foi ativada com sucesso. Agora você pode entrar", "auth.activationFailed": "Ativação falhou", "auth.activationFailedDescription": "Falha ao ativar sua conta. Por favor, tente novamente ou entre em contato com o suporte", "auth.backToLogin": "Voltar ao login", "auth.loginFailed": "<PERSON><PERSON> falhou", "auth.loginWithGoogle": "Entrar com Google", "auth.google": "Google", "auth.filter": "Filter", "validation.invalidEmail": "<PERSON><PERSON>", "validation.passwordMinLength": "A senha deve ter pelo menos 8 caracteres", "validation.nameRequired": "Nome é obrigatório", "validation.required": "Este campo é obrigatório", "validation.passwordsDoNotMatch": "As senhas não coincidem", "imageSelect.pleaseSelectImageFile": "Por favor, selecione um arquivo de imagem", "imageSelect.selectedImage": "Imagem se<PERSON>", "imageSelect.removeImage": "Remover imagem", "pixelReveal.loading": "Carregando imagem...", "pixelReveal.processing": "Processando imagem...", "pixelReveal.revealComplete": "Revelação da imagem completa", "SIGNIN_WRONG_EMAIL_PASSWORD": "<PERSON>ail ou senha incorretos", "Try again": "Tentar novamente", "aiToolMenu.imagen": "Imagen", "aiToolMenu.videoGen": "Video Gen", "aiToolMenu.speechGen": "Speech Gen", "aiToolMenu.musicGen": "Music Gen", "aiToolMenu.imagen3": "Imagen 3", "aiToolMenu.imagen3Description": "Gere imagens de alta qualidade e detalhadas com renderização de texto precisa para conteúdo visual criativo.", "aiToolMenu.imagen4": "Imagen 4", "aiToolMenu.imagen4Description": "Expresse suas ideias como nunca antes — com Imagen, a criatividade não tem limites.", "aiToolMenu.gemini2Flash": "Gemini 2.0 Flash", "aiToolMenu.gemini2FlashDescription": "Gemini 2.0 Flash é uma ferramenta poderosa para gerar imagens a partir de prompts de texto.", "aiToolMenu.veo2": "Veo 2", "aiToolMenu.veo2Description": "<PERSON><PERSON> controle, consistência e criatividade do que nunca.", "aiToolMenu.veo3": "Veo 3", "aiToolMenu.veo3Description": "<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>. Nosso mais recente modelo de geração de vídeo, projetado para capacitar cineastas e contadores de histórias.", "aiToolMenu.gemini25Pro": "Gemini 2.5 Pro", "aiToolMenu.gemini25ProDescription": "O modelo de texto para fala mais avançado disponível.", "aiToolMenu.gemini25Flash": "Gemini 2.5 Flash", "aiToolMenu.gemini25FlashDescription": "Processamento em larga escala (ex. múltiplos pdfs).\nTarefas de baixa latência e alto volume que requerem pensamento\nCasos de uso agênticos", "aiToolMenu.link": "Link", "aiToolMenu.linkDescription": "Use NuxtLink com superpoderes.", "aiToolMenu.soon": "Em breve", "readArticle": "Ler Artigo", "switchToLightMode": "Mudar para modo claro", "switchToDarkMode": "Mudar para modo escuro", "profile": "Perfil", "buyCredits.checkout": "Finalizar Compra", "buyCredits.checkoutDescription": "Confirme seu pedido e então escolha seu método de pagamento.", "buyCredits.orderDetail": "Detalhe do pedido", "buyCredits.credits": "C<PERSON>dit<PERSON>", "buyCredits.pricePerUnit": "Preço por unidade", "buyCredits.totalCredits": "Total de créditos", "buyCredits.totalPrice": "Preço total", "buyCredits.payment": "Pagamento", "buyCredits.submit": "Enviar", "buyCredits.cancel": "<PERSON><PERSON><PERSON>", "pricing.title": "Preços", "pricing.description": "Escolha o plano perfeito para suas necessidades de geração de imagens", "pricing.comingSoon": "Em Breve", "pricing.comingSoonDescription": "Nossos planos de preços estão sendo finalizados. Volte em breve para atualizações.", "magicImageDemo.title": "Efeito de Partículas de Imagem IA", "magicImageDemo.description": "Uma demonstração do componente BaseMagicImage que transforma partículas em imagens geradas por IA", "magicImageDemo.image": "Imagem", "magicImageDemo.aboutTitle": "Sobre Este Componente", "magicImageDemo.aboutDescription": "O componente BaseMagicImage usa Three.js para criar um sistema de partículas que pode se transformar entre posições aleatórias e uma imagem gerada por IA. As partículas se movem com efeitos de redemoinho e fluxo, criando uma transformação mágica.", "magicImageDemo.featuresTitle": "Recursos", "magicImageDemo.features.particleRendering": "Renderização de imagem baseada em partículas", "magicImageDemo.features.smoothTransitions": "Transições suaves entre posições aleatórias de partículas e formação de imagem", "magicImageDemo.features.interactiveControls": "Controles de câmera interativos (arrastar para girar, rolar para zoom)", "magicImageDemo.features.customizable": "Contagem de partículas e duração de animação personalizáveis", "magicImageDemo.features.automatic": "Ativação de transformação automática ou manual", "magicImageDemo.howItWorksTitle": "Como Funciona", "magicImageDemo.howItWorksDescription": "O componente analisa os pixels de uma imagem e cria um sistema de partículas 3D onde cada partícula representa um pixel. Pixels mais brilhantes são posicionados mais próximos ao visualizador, criando um efeito 3D sutil. As partículas são inicialmente espalhadas aleatoriamente no espaço 3D, então se animam para formar a imagem quando ativadas.", "privacy.title": "Política de Privacidade", "privacy.description": "Saiba como protegemos sua privacidade e lidamos com seus dados", "privacy.informationWeCollect": "Informações que Coletamos", "privacy.dataSecurity": "Segurança de Dados", "privacy.dataSecurityDescription": "Implementamos medidas de segurança apropriadas para proteger suas informações pessoais contra acesso não autorizado, alteração, divulgação ou destruição.", "privacy.contactUs": "Entre em Contato", "terms.title": "Termos de Serviço", "terms.description": "Termos e condições para usar os serviços Imagen", "terms.acceptanceOfTerms": "1. Aceitação dos Termos", "terms.acceptanceOfTermsDescription": "Ao acessar e usar os serviços Imagen, você aceita e concorda em estar vinculado aos termos e disposições deste acordo.", "terms.useOfService": "2. Uso do Serviço", "terms.userAccounts": "3. <PERSON><PERSON> de Usuário", "terms.userAccountsDescription": "Você é responsável por manter a confidencialidade de sua conta e senha.", "terms.intellectualProperty": "4. <PERSON><PERSON><PERSON>ade Intelectual", "terms.intellectualPropertyDescription": "Todo o conteúdo e materiais disponíveis em nosso serviço são protegidos por direitos de propriedade intelectual.", "terms.termination": "5. <PERSON><PERSON><PERSON><PERSON>", "terms.terminationDescription": "Podemos encerrar ou suspender sua conta e acesso ao serviço a nosso exclusivo critério.", "terms.disclaimers": "6. Isenções de Responsabilidade", "terms.disclaimersDescription": "O serviço é fornecido 'como está' sem garantias de qualquer tipo.", "terms.contactUsTerms": "Entre em Contato", "terms.contactUsTermsDescription": "Se você tiver alguma dúvida sobre estes Termos de Serviço, entre em contato conosco através de nossos canais de suporte.", "Describe the video you want to generate...": "Descreva o vídeo que você deseja gerar...", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "appName": "GeminiGen.AI", "quickTopUp": "<PERSON><PERSON><PERSON>", "customTopUp": "Recarga personalizada", "numberOfCredits": "Número de créditos", "paypal": "PayPal", "paypalDescription": "Pague com segurança usando sua conta PayPal.", "debitCreditCard": "Cartão de Débito ou Crédito", "cardDescription": "Visa, Mastercard, American Express", "payWithCrypto": "Pague com Cripto", "cryptoDescription": "Bitcoin, Ethereum e outras criptomoedas", "profileMenu.guide": "<PERSON><PERSON><PERSON>", "profileMenu.logo": "<PERSON><PERSON><PERSON>", "profileMenu.settings": "Configurações", "profileMenu.components": "Componentes", "loadingMoreItems": "Carregando mais itens...", "promptLabel": "Solicitação:", "videoExamples": "Exemplos de Vídeo", "videoExamplesDescription": "Explore estes exemplos de vídeo com seus prompts e configurações. Clique em qualquer botão 'Usar Este Prompt' para copiar o prompt para seu campo de entrada.", "useThisPrompt": "Use este prompt", "model": "<PERSON><PERSON>", "duration": "Duração", "videoTypeSelection": "Selecionar Tipo de Vídeo", "notifications.title": "Notifications", "notifications.description": "Your recent notifications and updates", "notifications.totalCount": "{count} notifications", "notifications.markAllRead": "Mark all as read", "notifications.loadMore": "Load more", "notifications.close": "Close", "notifications.empty.title": "No notifications", "notifications.empty.description": "You're all caught up! No new notifications to show.", "notifications.error.title": "Error loading notifications", "notifications.types.default.title": "Notification", "notifications.types.default.description": "You have a new notification", "notifications.types.video_1.title": "Geração de Vídeo Pendente", "notifications.types.video_1.description": "A geração de vídeo está aguardando para ser processada.", "notifications.types.video_2.title": "Geração de Vídeo Concluída", "notifications.types.video_2.description": "O vídeo foi gerado com sucesso.", "notifications.types.video_3.title": "Geração de Vídeo Falhou", "notifications.types.video_3.description": "Falha na geração de vídeo", "notifications.types.image_1.title": "Geração de Imagem Pendente", "notifications.types.image_1.description": "A geração de imagem está aguardando para ser processada.", "notifications.types.image_2.title": "Geração de Imagem Completa", "notifications.types.image_2.description": "A imagem foi gerada com sucesso.", "notifications.types.image_3.title": "Falha na Geração da Imagem", "notifications.types.image_3.description": "A geração de imagem falhou", "notifications.types.tts_history_1.title": "Geração de Áudio Pendente", "notifications.types.tts_history_1.description": "A conversão de texto em fala está aguardando para ser processada.", "notifications.types.tts_history_2.title": "Geração de Áudio Concluída", "notifications.types.tts_history_2.description": "Áudio de texto para fala foi gerado com sucesso.", "notifications.types.tts_history_3.title": "Falha na Geração de Áudio", "notifications.types.tts_history_3.description": "Falha na geração de texto para fala", "notifications.types.voice_training_1.title": "Treinamento de Voz Pendente", "notifications.types.voice_training_1.description": "O treinamento de voz está aguardando para ser processado.", "notifications.types.voice_training_2.title": "Treinamento de Voz Concluído", "notifications.types.voice_training_2.description": "O treinamento do modelo de voz personalizado foi concluído com sucesso.", "notifications.types.voice_training_3.title": "Treinamento de Voz Falhou", "notifications.types.voice_training_3.description": "Treinamento de voz falhou", "notifications.types.music_1.title": "Geração de Música Pendente", "notifications.types.music_1.description": "A geração de música está aguardando para ser processada.", "notifications.types.music_2.title": "Geração de Música Completa", "notifications.types.music_2.description": "Música de IA foi gerada com sucesso.", "notifications.types.music_3.title": "Geração de Música Falhou", "notifications.types.music_3.description": "Geração de música falhou", "notifications.types.speech_1.title": "Geração de Discurso Pendente", "notifications.types.speech_1.description": "Seu pedido de geração de discurso está aguardando para ser processado.", "notifications.types.speech_2.title": "Geração de Discurso Completa", "notifications.types.speech_2.description": "Seu discurso foi gerado com sucesso.", "notifications.types.speech_3.title": "Geração de Discurso Falhou", "notifications.types.speech_3.description": "A geração do seu discurso falhou. Por favor, tente novamente.", "notifications.time.justNow": "<PERSON><PERSON>a mesmo", "notifications.time.minutesAgo": "{minutes}m atrás", "notifications.time.hoursAgo": "{hours}h atrás", "notifications.time.yesterday": "Ontem", "notifications.status.processing.title": "Processamento", "notifications.status.processing.description": "Seu pedido está sendo processado.", "notifications.status.success.title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "notifications.status.success.description": "Concluído com sucesso", "notifications.status.failed.title": "<PERSON><PERSON><PERSON>", "notifications.status.failed.description": "Ocorreu um erro durante o processamento.", "notifications.status.warning.title": "Aviso", "notifications.status.warning.description": "Concluído com avisos", "notifications.status.pending.title": "Pendente", "notifications.status.pending.description": "Aguardando para ser processado", "notifications.status.cancelled.title": "Cancelado", "notifications.status.cancelled.description": "Solicitação foi cancelada", "footer.nuxtUIOnDiscord": "Nuxt UI on Discord", "profileSettings.emailNotifications": "Email Notifications", "profileSettings.marketingEmails": "Marketing Emails", "profileSettings.securityAlerts": "Security Alerts", "settings": "Configurações", "userMenu.profile": "Perfil", "userMenu.buyCredits": "<PERSON>mp<PERSON>", "userMenu.settings": "Configurações", "userMenu.api": "API", "userMenu.logout": "<PERSON><PERSON>", "userMenu.greeting": "<PERSON><PERSON><PERSON>, {name}", "formats.mp3": "MP3", "formats.wav": "WAV", "channels.mono": "Mono", "channels.stereo": "Estéreo", "options.allow": "<PERSON><PERSON><PERSON>", "options.dontAllow": "Não permitir", "options.voices": "Vozes", "options.pickVoice": "E<PERSON>lher voz", "voiceTypes.systemVoices": "Vozes do sistema", "voiceTypes.customVoices": "<PERSON>ozes personaliza<PERSON>", "voiceTypes.premiumVoices": "Vozes premium", "voiceTypes.userVoices": "Vozes do usuário", "common.home": "Casa", "Describe the speech you want to generate...": "Descreva o discurso que você deseja gerar...", "listenToSpeech": "<PERSON><PERSON><PERSON> discurso", "generateSimilar": "<PERSON><PERSON><PERSON>", "voice": "Voz", "emotion": "Emoção", "speed": "Velocidade", "speed_settings": "Configurações de Velocidade", "speed_value": "Valor de Velocidade", "speed_slider": "Controle de Velocidade", "apply": "Aplicar", "speech_settings": "Configurações de Fala", "current_speed": "Velocidade Atual", "reset_defaults": "<PERSON><PERSON><PERSON>", "outputFormat": "Formato de Saída", "outputChannel": "Canal de Saída", "selectVoice": "Selecionar Voz", "selectEmotion": "Selecionar Emoção", "selectFormat": "Selecionar Formato", "selectChannel": "Selecionar Canal", "noVoicesAvailable": "Sem vozes disponíveis", "noEmotionsAvailable": "Sem emoções disponíveis", "searchVoices": "Procurar vozes...", "searchEmotions": "Buscar emoções...", "noVoicesFound": "Nenhuma voz encontrada", "noEmotionsFound": "Nenhuma emoção encontrada", "retry": "Tentar novamente", "noAudioSample": "Sem amostra de áudio disponível", "Speech Generation Complete": "Geração de fala concluída", "Your speech has been generated successfully": "Seu discurso foi gerado com sucesso.", "stripe": "Stripe", "stripeDescription": "Pague com segurança usando o Stripe", "history.tabs.imagen": "Imagem", "history.tabs.video": "Vídeo", "history.tabs.speech": "Discurso", "history.tabs.music": "Música", "history.tabs.history": "História", "orders.title": "Histórico de Pedidos", "orders.description": "Veja seu histórico de transações e pagamentos.", "orders.orderId": "ID do Pedido", "orders.amount": "Quantia", "orders.credits": "C<PERSON>dit<PERSON>", "orders.quantity": "Quantidade", "orders.platform": "Plataforma", "orders.externalId": "ID da Transação", "orders.status.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "orders.status.success": "Sucesso", "orders.status.paid": "Pago", "orders.status.pending": "Pendente", "orders.status.processing": "Processamento", "orders.status.failed": "Fal<PERSON>", "orders.status.cancelled": "Cancelado", "orders.status.error": "Erro", "orders.empty.title": "<PERSON>da sem pedidos.", "orders.empty.description": "Você ainda não fez nenhum pedido. Compre créditos para começar a usar nossos serviços.", "orders.empty.action": "<PERSON><PERSON><PERSON>", "orders.endOfList": "Você viu todos os pedidos.", "orders.errors.fetchFailed": "Falha ao carregar o histórico de pedidos. Por favor, tente novamente.", "orders.meta.title": "Histórico de Pedidos - Imagen AI", "orders.meta.description": "Visualize seu histórico de transações e pagamentos na Imagen AI", "historyPages.imagenDescription": "Navegue pelas suas imagens e obras de arte geradas por IA.", "historyPages.musicDescription": "Navegue pelo seu conteúdo musical e de áudio gerado por IA.", "historyPages.speechDescription": "Navegue pelo seu conteúdo de fala e voz gerado por IA.", "historyPages.videoDescription": "Navegue pelos seus vídeos e animações gerados por IA", "historyPages.imagenBreadcrumb": "Imagem", "historyPages.musicBreadcrumb": "Música", "historyPages.speechBreadcrumb": "Discurso", "historyPages.videoBreadcrumb": "Geração de Vídeo", "historyPages.endOfImagesHistory": "Você chegou ao fim do histórico de imagens.", "historyPages.endOfMusicHistory": "Você chegou ao fim da história da música.", "historyPages.endOfSpeechHistory": "Você chegou ao fim do histórico de discursos.", "historyPages.endOfVideoHistory": "Você chegou ao final do histórico de vídeos.", "historyPages.noVideosFound": "Nenhum vídeo encontrado", "historyPages.noVideosFoundDescription": "Comece a gerar vídeos para vê-los aqui.", "historyPages.backToLibrary": "Voltar para a Biblioteca", "historyPages.errorLoadingVideo": "Erro ao Carregar Vídeo", "historyPages.loadingVideoDetails": "Carregando detalhes do vídeo...", "historyPages.videoDetails": "Detalhes do Vídeo", "historyPages.videoInformation": "Informações do Vídeo", "historyPages.videoNotFound": "O vídeo que você está procurando não pôde ser encontrado ou carregado.", "historyPages.aiContentLibraryTitle": "Biblioteca de Conteúdo de IA", "historyPages.aiContentLibraryDescription": "Navegue e gerencie seu conteúdo gerado por IA em diferentes categorias.", "demo.notifications.title": "Tipos de Notificação e Demonstração de Status", "demo.notifications.description": "Exemplos de diferentes tipos de notificações com vários estados de status", "demo.notifications.statusLegend": "Legenda de Status", "demo.speechVoiceSelect.title": "Demonstração de Seleção de Voz de Fala", "demo.speechVoiceSelect.description": "Demonstrando o componente reutilizável BaseSpeechVoiceSelectModal com a propriedade modelValue", "aspectRatio": "Proporção de Tela", "Image Reference": "Referência de Imagem", "personGeneration.dontAllow": "Don't Allow", "personGeneration.allowAdult": "Allow Adult", "personGeneration.allowAll": "Allow All", "safety_filter_level": "Nível de Filtro de Segurança", "used_credit": "<PERSON><PERSON><PERSON><PERSON>", "Safety Filter": "Filtro de Segurança", "safetyFilter.blockLowAndAbove": "Bloqueio Baixo e Acima", "safetyFilter.blockMediumAndAbove": "Bloquear Médio e Superior", "safetyFilter.blockOnlyHigh": "Bloquear Apenas Alto", "safetyFilter.blockNone": "Bloquear Nenhum", "historyFilter.all": "Todos", "historyFilter.imagen": "Imagem", "historyFilter.videoGen": "Vídeo Gen", "historyFilter.speechGen": "Geração de Discurso", "Person Generation": "Geração de Pessoas", "downloadImage": "Baixar imagem", "noImageAvailable": "Imagem não disponível", "enhancePrompt": "Apr<PERSON><PERSON> Prompt", "addImages": "<PERSON><PERSON><PERSON><PERSON>", "generateVideo": "<PERSON><PERSON><PERSON>", "happy": "<PERSON><PERSON><PERSON>", "sad": "Triste", "angry": "<PERSON><PERSON>", "excited": "Animado", "laughing": "<PERSON><PERSON><PERSON>", "crying": "Chorando", "calm": "Calma", "serious": "<PERSON><PERSON><PERSON>", "frustrated": "<PERSON><PERSON><PERSON>", "hopeful": "Esperançoso", "narrative": "<PERSON><PERSON><PERSON><PERSON>", "kids' storytelling": "Histórias para Crianças", "audiobook": "Audiolivro", "poetic": "Poético", "mysterious": "Misterioso", "inspirational": "Inspirador", "surprised": "Surpreso", "confident": "<PERSON><PERSON><PERSON>", "romantic": "R<PERSON><PERSON><PERSON><PERSON>", "scared": "<PERSON><PERSON><PERSON><PERSON>", "trailer voice": "Voz do Trailer", "advertising": "Publicidade", "documentary": "Documentário", "newsreader": "<PERSON><PERSON>í<PERSON>", "weather report": "Relatório do Tempo", "game commentary": "Comentário do Jogo", "interactive": "Interativo", "customer support": "Apoio ao Cliente", "playful": "Divertido", "tired": "Cansado", "sarcastic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "disgusted": "<PERSON><PERSON><PERSON>", "whispering": "Sussurrando", "persuasive": "<PERSON><PERSON><PERSON><PERSON>", "nostalgic": "Nostálgico", "meditative": "Meditativo", "announcement": "<PERSON><PERSON><PERSON>", "professional pitch": "Apresentação Profissional", "casual": "Casual", "exciting trailer": "Trailer <PERSON>", "dramatic": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "corporate": "Corporativo", "tech enthusiast": "Entusiasta da Tecnologia", "youthful": "Juvenil", "calming reassurance": "Tranquilizante Reafirmação", "heroic": "Heroico", "festive": "<PERSON><PERSON>", "urgent": "Urgente", "motivational": "Motivacional", "friendly": "Amigável", "energetic": "Energético", "serene": "<PERSON><PERSON>", "bold": "Negrito", "charming": "Encan<PERSON><PERSON>", "monotone": "Monótono", "questioning": "Questionamento", "directive": "Diretiva", "dreamy": "<PERSON><PERSON><PERSON>", "epic": "Épico", "lyrical": "<PERSON><PERSON><PERSON><PERSON>", "mystical": "Místico", "melancholy": "Melancolia", "cheerful": "Alegre", "eerie": "<PERSON><PERSON><PERSON><PERSON>", "flirtatious": "Fler<PERSON><PERSON>", "thoughtful": "Reflexivo", "cinematic": "Cinemático", "humorous": "Humorístico", "instructional": "Instrucional", "conversational": "Conversacional", "apologetic": "Apologé<PERSON><PERSON>", "excuse-making": "Elaboração de desculpas", "encouraging": "Encorajador", "neutral": "Neutro", "authoritative": "Autoritário", "sarcastic cheerful": "Sarcástico Alegre", "reassuring": "Reconfortante", "formal": "Formal", "anguished": "<PERSON><PERSON><PERSON>", "giggling": "<PERSON><PERSON><PERSON>", "exaggerated": "Exagerado", "cold": "<PERSON><PERSON>", "hot-tempered": "Temperamental", "grateful": "Grato", "regretful": "<PERSON><PERSON>pendi<PERSON>", "provocative": "Provocativo", "triumphant": "<PERSON><PERSON><PERSON><PERSON>", "vengeful": "Vingativo", "heroic narration": "Narração Heroica", "villainous": "Vilãnsco", "hypnotic": "Hipnótico", "desperate": "Desesperado", "lamenting": "<PERSON><PERSON><PERSON><PERSON>", "celebratory": "Comemorativo", "teasing": "Provocação", "exhausted": "<PERSON><PERSON><PERSON>", "questioning suspicious": "<PERSON><PERSON><PERSON>", "optimistic": "Otimista", "bright, gentle voice, expressing excitement.": "Voz clara e suave, expressando en<PERSON>.", "low, slow voice, conveying deep emotions.": "Voz baixa e lenta, transmitindo emoções profundas.", "sharp, exaggerated voice, expressing frustration.": "Voz aguda e exagerada, expressando frustração.", "fast, lively voice, full of enthusiasm.": "Voz rápida e animada, cheia de entusiasmo.", "interrupted, joyful voice, interspersed with laughter.": "Voz interrompida e alegre, entremeada de risadas.", "shaky, low voice, expressing pain.": "Voz trêmula e baixa, expressando dor.", "gentle, steady voice, providing reassurance.": "Voz suave e constante, proporcionando segu<PERSON>.", "mature, clear voice, suitable for formal content.": "Voz madura e clara, adequada para conteúdo formal.", "weary, slightly irritated voice.": "Voz cansada, ligeiramente irritada.", "bright voice, conveying positivity and hope.": "Voz radiante, transmitindo positividade e esperança.", "natural, gentle voice with a slow rhythm.": "Voz natural e suave com um ritmo lento.", "lively, engaging voice, captivating for children.": "Voz animada e envolvente, cativante para crianças.", "even, slow voice, emphasizing content meaning.": "<PERSON>oz calma, pausada, valorizando o significado do conteúdo.", "rhythmic, emotional voice, conveying subtlety.": "Voz rítmica e emocional, transmitindo sutileza.", "low, slow voice, evoking curiosity.": "Voz baixa e lenta, evocando curiosidade.", "strong, passionate voice, driving action.": "Voz forte e apaixonada, impulsionando a ação.", "high, interrupted voice, expressing astonishment.": "<PERSON>oz alta, interrompida, expressando espanto.", "firm, powerful voice, persuasive and assuring.": "Voz firme, poderosa, persuasiva e tranquilizadora.", "sweet, gentle voice, suitable for emotional content.": "Voz doce e suave, adequada para conteúdo emocional.", "shaky, interrupted voice, conveying anxiety.": "Voz trêmula e interrompida, transmitindo ansiedade.", "deep, strong voice with emphasis, creating suspense.": "Voz profunda e forte com ênfase, criando suspense.", "engaging, lively voice, emphasizing product benefits.": "Voz envolvente e animada, destacando os benefícios do produto.", "formal, clear voice with focus on key points.": "Voz formal e clara com foco nos pontos principais.", "calm, profound voice, delivering authenticity.": "Voz calma e profunda, transmitindo autenticidade.", "standard, neutral voice, clear and precise.": "<PERSON><PERSON> pad<PERSON>, neutra, clara e precisa.", "bright, neutral voice, suitable for concise updates.": "Voz clara e neutra, adequada para atualizações concisas.", "fast, lively voice, stimulating excitement.": "Voz rápida e animada, estimulando a excitação.", "friendly, approachable voice, encouraging engagement.": "Voz amigável e acessível, incentivando o engajamento.", "empathetic, gentle voice, easy to connect with.": "Voz empática, amável e de fácil conex<PERSON>.", "clear voice, emphasizing questions and answers.": "<PERSON>oz clara, enfatizando perguntas e respostas.", "cheerful, playful voice with a hint of mischief.": "Voz alegre e brincalhona com um toque de travessura.", "slow, soft voice lacking energy.": "Voz lenta e suave, sem energia.", "ironic, sharp voice, sometimes humorous.": "Voz irônica, afiada, por vezes humorística.", "cold voice, clearly expressing discomfort.": "<PERSON>oz fria, expressando claramente desconforto.", "soft, mysterious voice, creating intimacy.": "Voz suave e misteriosa, criando intimidade.", "emotional voice, convincing the listener to act.": "<PERSON>oz emocional, convencendo o ouvinte a agir.", "gentle voice, evoking feelings of reminiscence.": "<PERSON>oz suave, evocando sentimentos de reminiscência.", "even, relaxing voice, suitable for mindfulness.": "Voz uniforme e relaxante, adequada para a atenção plena.", "clear voice, emphasizing key words.": "<PERSON>oz clara, enfatizando palavras-chave.", "confident, clear voice, ideal for business presentations.": "Voz confiante e clara, ideal para apresentações empresariais.", "natural, friendly voice, as if talking to a friend.": "Voz natural e amigável, como se estivesse conversando com um amigo.", "fast, powerful voice, creating tension and excitement.": "Voz rápida e poderosa, criando tensão e empolgação.", "emphasized, suspenseful voice, creating intensity.": "Voz enfatizada e carregada de suspense, criando intensidade.", "professional, formal voice, suitable for business content.": "Voz profissional e formal, adequada para conteúdo empresarial.", "energetic, lively voice, introducing new technologies.": "Voz enérgica e animada, introduzindo novas tecnologias.", "vibrant, cheerful voice, appealing to younger audiences.": "Voz vibrante e alegre, atraente para públicos mais jovens.", "gentle, empathetic voice, easing concerns.": "Voz gentil e empática, acalmando preocupações.", "strong, decisive voice, full of inspiration.": "Voz forte e decisiva, cheia de inspiração.", "bright, excited voice, suitable for celebrations.": "Voz brilhante e animada, adequada para celebrações.", "fast, strong voice, emphasizing urgency.": "Voz rápida e forte, enfatizando urgência.", "passionate, inspiring voice, encouraging action.": "Voz apaixonada e inspiradora, incentivando a ação.", "warm, approachable voice, fostering connection.": "Voz calorosa e acessível, promovendo <PERSON>.", "fast, powerful voice, brimming with enthusiasm.": "Voz rápida e poderosa, repleta de entusiasmo.", "slow, gentle voice, evoking peace and tranquility.": "Voz lenta e suave, evocando paz e tranquilidade.", "firm, assertive voice, exuding confidence.": "Voz firme e assertiva, exalando confiança.", "warm, captivating voice, leaving a strong impression.": "Voz quente e cativante, deixando uma forte impressão.", "flat, unvaried voice, conveying neutrality or irony.": "Voz plana e invariante, transmitindo neutralidade ou ironia.", "curious voice, emphasizing questions.": "<PERSON>oz curiosa, enfatizando pergun<PERSON>.", "firm, clear voice, guiding the listener step-by-step.": "Voz firme e clara, guiando o ouvinte passo a passo.", "gentle, slow voice, evoking a floating sensation.": "Voz suave e lenta, evocando uma sensação de flutuar.", "deep, resonant voice, emphasizing grandeur.": "Voz profunda e ressonante, enfatizando a grandiosidade.", "soft, melodic voice, similar to singing.": "Voz suave e melódica, semelhante a canto.", "low, drawn-out voice, evoking mystery.": "Voz baixa e arrastada, evocando mistério.", "slow, low voice, conveying deep sadness.": "Voz lenta e baixa, transmitindo profunda tristeza.", "bright, energetic voice, full of positivity.": "Voz brilhante e energética, cheia de positividade.", "low, whispery voice, evoking fear or strangeness.": "Voz baixa e sussurrante, evocando medo ou estranheza.", "sweet, teasing voice, full of allure.": "<PERSON>oz doce, provocante, cheia de encanto.", "slow, reflective voice, full of contemplation.": "Voz lenta e reflexiva, cheia de contemplação.", "resonant, emphasized voice, creating a movie-like effect.": "Voz ressonante e enfatizada, criando um efeito cinematográfico.", "lighthearted, cheerful voice, sometimes exaggerated.": "Voz alegre e animada, às vezes exagerada.", "clear, slow voice, guiding the listener step-by-step.": "Voz clara e pausada, guiando o ouvinte passo a passo.", "natural voice, as if chatting with the listener.": "Voz natural, como se estivesse conversando com o ouvinte.", "soft, sincere voice, expressing regret.": "Voz suave e sincera, expressando arrependimento.", "hesitant, uncertain voice, sometimes awkward.": "<PERSON><PERSON> <PERSON><PERSON>, incerta, às vezes desajeitada.", "warm voice, providing motivation and support.": "Voz acolhedora, proporcionando motivação e apoio.", "even voice, free of emotional bias.": "Voz equilibrada, livre de viés emocional.", "strong, powerful voice, exuding credibility.": "Voz forte e poderosa, exalando credibilidade.", "cheerful voice with an undertone of mockery.": "Voz alegre com um tom de zombaria.", "gentle, empathetic voice, providing comfort.": "Voz suave e empática, oferecendo conforto.", "clear, polite voice, suited for formal occasions.": "Voz clara e educada, adequada para ocasiões formais.", "urgent, shaky voice, expressing distress.": "<PERSON><PERSON><PERSON>, voz tr<PERSON><PERSON><PERSON>, <PERSON>ando an<PERSON>.", "interrupted voice, mixed with light laughter.": "Voz interrompida, misturada com risadinhas.", "loud, emphasized voice, often humorous.": "Voz alta e enfatizada, muitas vezes humorística.", "flat, unemotional voice, conveying detachment.": "<PERSON>oz plana, sem emoção, transmitindo distanciamento.", "fast, sharp voice, sometimes out of control.": "<PERSON>oz r<PERSON>a, aguda, às vezes fora de controle.", "warm, sincere voice, expressing appreciation.": "Voz calorosa e sincera, expressando gratid<PERSON>.", "low, subdued voice, full of remorse.": "Voz baixa e contida, cheia de remorso.", "challenging, strong voice, full of insinuation.": "<PERSON><PERSON><PERSON><PERSON>, voz forte, cheia de insinuação.", "loud, powerful voice, full of victory.": "Voz alta e poderosa, cheia de vitória.", "low, cold voice, expressing determination for revenge.": "Voz baixa e fria, expressando determinação por vingança.", "strong, inspiring voice, emphasizing heroic deeds.": "Voz forte e inspiradora, enfatizando feitos heróicos.", "low, drawn-out voice, full of scheming.": "Voz baixa e prolongada, cheia de tramas.", "even, repetitive voice, drawing the listener in.": "Voz uniforme e repetitiva, cativando o ouvinte.", "urgent, shaky voice, expressing hopelessness.": "<PERSON><PERSON><PERSON>, voz trê<PERSON>la, expressando desespero.", "low, sorrowful voice, as if mourning.": "<PERSON>oz baixa e triste, como se estivesse de luto.", "excited, joyful voice, full of festive spirit.": "<PERSON><PERSON> em<PERSON>ga<PERSON>, alegre, cheia de espírito festivo.", "light, playful voice, sometimes mockingly.": "Voz leve e brincalhona, às vezes zombeteira.", "weak, broken voice, expressing extreme fatigue.": "Voz fraca e quebrada, expressando fadiga extrema.", "slow, emphasized voice, full of suspicion.": "<PERSON><PERSON> lenta, en<PERSON><PERSON><PERSON><PERSON>, cheia de suspeita.", "bright, hopeful voice, creating positivity.": "Voz radiante e esperançosa, criando positividade.", "Your audio will be processed with the latest stable model.": "Seu áudio será processado com o modelo estável mais recente.", "Your audio will be processed with the latest beta model.": "Seu áudio será processado com o modelo beta mais recente.", "You don't have any saved prompts yet.": "Você ainda não tem prompts salvos.", "Commercial Use": "<PERSON><PERSON>l", "Other people’s privacy": "A privacidade de outras pessoas", "You must respect the privacy of others when using our services. Do not upload or create speech output containing personal information, confidential data, or copyrighted material without permission.": "Você deve respeitar a privacidade dos outros ao usar nossos serviços. Não carregue ou crie saídas de fala que contenham informações pessoais, dados confidenciais ou material protegido por direitos autorais sem permissão.", "{price}$ per credit": "{price}$ por crédito", "Pricing": "Preços", "Simple and flexible. Only pay for what you use.": "Simples e flexível. Pague apenas pelo que usar.", "Pay as you go": "Pague conforme o uso", "Flexible": "Flexível", "Input characters": "Caracteres de entrada", "Audio model": "<PERSON><PERSON>", "Credits": "C<PERSON>dit<PERSON>", "Cost": "Custo", "HD quality voices": "Vozes de qualidade HD", "Advanced model": "<PERSON><PERSON>", "Buy now": "Compre agora", "Paste your text to calculate": "Cole seu texto para calcular", "Paste your text here...": "Cole seu texto aqui...", "Calculate": "Calcular", "Estimate your cost by drag the slider below or": "Estime seu custo arrastando o controle deslizante abaixo ou", "calming": "Calmante", "customer": "Cliente", "exciting": "empol<PERSON>e", "excuse": "<PERSON><PERSON><PERSON><PERSON>", "game": "Jogo", "hot": "<PERSON><PERSON>", "kids": "Crian<PERSON><PERSON>", "professional": "Profissional", "tech": "Tecnologia", "trailer": "Reboque", "weather": "Tempo", "No thumbnail available": "Nenhuma miniatura disponível", "Debit or Credit Card": "Cartão de Débito ou Crédito", "Visa, Mastercard, American Express and more": "Visa, Mastercard, American Express e mais", "Top up now": "Recar<PERSON><PERSON> agora", "noVideoAvailable": "Sem vídeo disponível", "common.back": "Voltar", "common.edit": "<PERSON><PERSON>", "common.save": "<PERSON><PERSON>", "common.cancel": "<PERSON><PERSON><PERSON>", "common.delete": "Excluir", "common.copy": "Copiar", "common.copied": "Copiado", "common.manage": "Gerenciar", "aiToolMenu.textToImage": "Texto para Imagem", "profileMenu.integration": "Integração", "videoTypes.examples.tikTokDanceTrend": "Tendência de Dança do TikTok", "videoTypes.examples.energeticDanceDescription": "Vídeo de dança energética com cores vibrantes, cortes rápidos, música em alta, formato vertical, estilo redes sociais.", "videoTypes.examples.instagramReel": "Reel do Instagram", "videoTypes.examples.lifestyleDescription": "Conteúdo de estilo de vida com visuais estéticos, transições suaves, efeitos modernos, narrativa envolvente", "videoTypes.examples.comedySketch": "Esquete de Comédia", "videoTypes.examples.funnyComedyDescription": "Cena de comédia engraçada com personagens expressivos, situações humorísticas, diálogo divertido, clima leve.", "videoTypes.examples.productLaunchAd": "Anúncio de Lançamento de Produto", "videoTypes.examples.professionalCorporateDescription": "Vídeo corporativo profissional com apresentação executiva, ambiente de escritório limpo, estilo formal de negócios", "videoTypes.examples.quickPromoVideo": "Vídeo Promocional Rápido", "videoTypes.examples.fastPacedPromoDescription": "Conteúdo promocional dinâmico com produção eficiente, visuais econômicos, mensagem simplificada.", "videoTypes.examples.birthdayGreeting": "Cumprimento de Aniversário", "videoTypes.examples.personalizedBirthdayDescription": "Vídeo de aniversário personalizado com decorações festivas, iluminação acolhedora, atmosfera de celebração, mensagem sincera", "videoTypes.examples.brandStoryVideo": "Vídeo de História da Marca", "videoTypes.examples.tutorialVideo": "Ví<PERSON>o <PERSON>", "videoTypes.examples.manOnThePhone": "Homem no telefone", "videoTypes.examples.runningSnowLeopard": "<PERSON><PERSON><PERSON>-<PERSON>-neves correndo", "videoTypes.examples.snowLeopard": "Leopardo-da-neve", "videoTypes.styles.cartoonAnimated": "Vídeo em estilo de desenho animado ou animação", "videoTypes.styles.naturalDocumentary": "Filmagem documental em estilo natural", "videoTypes.styles.naturalLifelike": "Estilo de vídeo natural e realista", "videoTypes.styles.professionalMovieQuality": "Qualidade profissional de cinema com iluminação dramática", "videoTypes.styles.creativeStylized": "Efeitos de vídeo criativos e estilizados", "videoTypes.styles.retroVintage": "Estética de vídeo retrô ou vintage", "historyFilter.dialogueGen": "Diálogo Gen", "historyFilter.speechGenDocument": "Geração de Discurso a partir de Documento", "demo.notifications.availableNotificationTypes": "Tipos de Notificação Disponíveis", "demo.speechVoiceSelect.example1": "Exemplo 1: <PERSON><PERSON>", "demo.speechVoiceSelect.example2": "Exemplo 2: <PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.example3": "Exemplo 3: <PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.example4": "Exemplo 4: <PERSON><PERSON><PERSON><PERSON> Exemplos de Erro", "demo.speechVoiceSelect.example5": "Exemplo 5: Comparação de Status", "demo.speechVoiceSelect.mainNarrator": "<PERSON><PERSON><PERSON>", "demo.speechVoiceSelect.characterVoice": "Voz do Personagem", "demo.speechVoiceSelect.selectedVoicesSummary": "Resumo de Vozes Selecionadas:", "demo.speechVoiceSelect.clearAll": "<PERSON><PERSON>", "demo.speechVoiceSelect.setRandomVoices": "<PERSON><PERSON><PERSON>oz<PERSON>", "demo.speechVoiceSelect.logToConsole": "Registrar no Console", "demo.speechVoiceSelect.notSelected": "Não selecionado", "demo.speechVoiceSelect.voiceSelectionsChanged": "Seleções de voz alteradas", "demo.historyWrapper.title": "Demonstração do Emblema de Status do HistoryWrapper", "demo.historyWrapper.normalStatus": "Exemplo 1: Status Normal (status = 1)", "demo.historyWrapper.processingStatus": "Exemplo 2: Status de Processamento (status = 2)", "demo.historyWrapper.errorStatus": "Exemplo 3: Status de Erro (status = 3) - Mostra Emblema de Erro", "demo.historyWrapper.multipleErrorExamples": "Exemplo 4: <PERSON><PERSON><PERSON><PERSON> Exemplos de Erros", "demo.historyWrapper.statusComparison": "Exemplo 5: Comparação de Status", "demo.historyWrapper.normalImageGeneration": "Geração de Imagens Normais", "demo.historyWrapper.videoGenerationInProgress": "Geração de Vídeo em Andamento", "demo.historyWrapper.speechGenerationFailed": "Falha na Geração de Fala", "demo.historyWrapper.imageFailed": "<PERSON><PERSON> falhou", "demo.historyWrapper.videoFailed": "<PERSON><PERSON><PERSON><PERSON> fal<PERSON>", "demo.historyWrapper.speechFailed": "Fal<PERSON> na Fala", "demo.historyWrapper.statusSuccess": "Status: Sucesso", "demo.historyWrapper.statusProcessing": "Status: <PERSON><PERSON><PERSON>", "demo.historyWrapper.statusError": "Status: Erro", "demo.historyWrapper.status1Success": "Status 1: Sucesso", "demo.historyWrapper.status2Processing": "Status 2: <PERSON><PERSON><PERSON>", "demo.historyWrapper.badgeBehavior": "Comportamento do Distintivo:", "demo.historyWrapper.showsOnlyTypeAndStyle": "Mostra apenas crachás de tipo e estilo", "demo.historyWrapper.showsTypeStyleAndError": "Mostra tipo, estilo e selo de erro vermelho com ícone de alerta.", "demo.historyWrapper.redBackgroundWithWhite": "Fundo vermelho com texto branco e ícone de círculo de alerta", "demo.historyWrapper.allBadgesHideOnHover": "Todos os emblemas se ocultam ao passar o mouse para mostrar o conteúdo da sobreposição.", "demo.speechVoiceCaching.title": "Teste de Cacheamento de Voz do Discurso", "demo.speechVoiceCaching.description": "Teste para verificar o cache de vozes entre diferentes componentes.", "demo.speechVoiceCaching.component1Modal": "Componente 1 - Modal", "demo.speechVoiceCaching.component3RegularSelect": "Componente 3 - Seleção Regular", "demo.speechVoiceCaching.forceReloadVoices": "Re<PERSON><PERSON>gar <PERSON>ças de Voz", "demo.speechVoiceCaching.clearAllSelections": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON><PERSON>", "demo.speechVoiceCaching.logStoreState": "Estado do Armazém de Registros", "demo.speechVoiceCaching.refreshPageInstructions": "Atualize a página e abra qualquer componente - isso recarregará.", "demo.speechVoiceCaching.checkNetworkTab": "Verifique a aba Network para confirmar as chamadas de API.", "demo.speechVoiceCaching.selectedVoicePersist": "A voz selecionada será mantida através do localStorage.", "demo.speechVoiceCaching.pageMounted": "<PERSON><PERSON><PERSON><PERSON> montada, a loja será auto-inicializada se necessário", "integration.title": "Integração", "integration.subtitle": "Gerencie suas chaves de API e configurações de integração.", "integration.apiKeys": "<PERSON>ves de <PERSON>", "integration.apiKeysDescription": "Gerencie suas chaves de API para acesso programático", "integration.webhook": "Webhook", "integration.webhookDescription": "Configurar URL do webhook para notificações", "apiKeys.title": "<PERSON>ves de <PERSON>", "apiKeys.subtitle": "Gerencie suas chaves de API para acesso programático", "apiKeys.create": "<PERSON><PERSON><PERSON><PERSON>", "apiKeys.createNew": "Criar nova chave de API", "apiKeys.createFirst": "Criar Primeira Chave de API", "apiKeys.name": "Nome", "apiKeys.nameDescription": "Dê ao seu chave de API um nome descritivo", "apiKeys.namePlaceholder": "por exemplo, chave de API do meu aplicativo", "apiKeys.nameRequired": "Nome da chave da API é obrigatório", "apiKeys.createdAt": "<PERSON><PERSON><PERSON>", "apiKeys.noKeys": "Sem chaves de API", "apiKeys.noKeysDescription": "Crie sua primeira chave de API para começar a acessar programaticamente.", "apiKeys.created": "Chave da API criada com sucesso", "apiKeys.createError": "Falha ao criar chave de API", "apiKeys.deleted": "Chave de API excluída com sucesso", "apiKeys.deleteError": "Falha ao excluir a chave de API", "apiKeys.deleteConfirm": "Excluir chave da API", "apiKeys.deleteWarning": "Você tem certeza de que deseja excluir esta chave de API? Esta ação não pode ser desfeita.", "apiKeys.copied": "Chave da API copiada para a área de transferência.", "apiKeys.copyError": "Falha ao copiar a chave da API", "webhook.title": "Configuração de Webhook", "webhook.subtitle": "Configure o URL do webhook para notificações em tempo real", "webhook.configuration": "URL do Webhook", "webhook.currentUrl": "URL atual do webhook", "webhook.currentUrlDescription": "Este URL receberá solicitações POST para eventos de webhook.", "webhook.notConfigured": "Nenhuma URL de webhook configurada", "webhook.url": "URL do Webhook", "webhook.urlDescription": "Insira a URL onde você deseja receber notificações de webhook", "webhook.urlPlaceholder": "https://your-domain.com/webhook", "webhook.urlRequired": "Por favor, insira um URL de webhook primeiro.", "webhook.invalidUrl": "Por favor, insira uma URL válida.", "webhook.saved": "URL do webhook salva com sucesso.", "webhook.saveError": "Falha ao salvar o URL do webhook", "webhook.test": "<PERSON>e", "webhook.testSent": "Teste Enviado", "webhook.testDescription": "Teste do webhook enviado com sucesso", "webhook.information": "Informações do Webhook", "webhook.howItWorks": "Como funciona", "webhook.description": "<PERSON>uando configurado, enviaremos solicitações HTTP POST para o URL do seu webhook sempre que certos eventos ocorrerem na sua conta.", "webhook.events": "Eventos de Webhook", "webhook.imageGenerated": "Geração de imagem concluída", "webhook.imageGenerationFailed": "Falha na geração de imagem", "webhook.creditUpdated": "Saldo de crédito atualizado", "webhook.payloadFormat": "Formato de Carga Útil", "webhook.payloadDescription": "As solicitações de webhook serão enviadas como JSON com a seguinte estrutura:", "webhook.security": "Segurança", "webhook.securityDescription": "Recomendamos o uso de URLs HTTPS e a implementação de verificação de assinatura para garantir a autenticidade do webhook.", "error.general": "Erro", "error.validation": "Erro de Validação", "error.required": "Campo obrigatório", "success.saved": "Salvo com sucesso", "success.created": "Criado com sucesso", "success.deleted": "Excluído com sucesso", "success.copied": "Copiado para a área de transferência", "confirmDelete": "Confirmar exclusão", "confirmDeleteDescription": "Tem certeza de que deseja excluir este item? Esta ação não pode ser desfeita.", "historyDeleted": "Item do histórico excluído com sucesso.", "deleteError": "Falha ao excluir item do histórico", "Regenerate Image": "Regenerar Imagem", "You haven't made any changes to the settings. Are you sure you want to regenerate the same image?": "Você não fez nenhuma alteração nas configurações. Você tem certeza de que deseja regenerar a mesma imagem?", "Yes, Regenerate": "<PERSON><PERSON>, <PERSON><PERSON>ar", "Cancel": "<PERSON><PERSON><PERSON>", "models.imagen4Fast": "Imagem 4 Rápida", "models.imagen4Ultra": "Imagen 4 Ultra", "voiceTypes.favoriteVoices": "<PERSON>oz<PERSON>", "voiceTypes.geminiVoices": "<PERSON><PERSON><PERSON> de Gêmeos", "speech.dialogueGeneration.complete": "Geração de Diálogo Completa", "speech.dialogueGeneration.failed": "Geração de Diálogo Falhou", "speech.dialogueGeneration.pending": "Geração de Diálogo Pendente", "speech.dialogueGeneration.dialogueGen": "Geração de Diálogo", "speech.dialogueGeneration.successMessage": "Seu diálogo foi gerado com sucesso.", "speech.speechGeneration.complete": "Geração de Discurso Completa", "speech.speechGeneration.failed": "Falha na Geração de Discurso", "speech.speechGeneration.pending": "Geração de Discurso Pendente", "speech.speechGeneration.successMessage": "Seu discurso foi gerado com sucesso.", "speech.speechGeneration.requestWaiting": "Seu pedido de geração de fala está aguardando para ser processado.", "speech.errors.failedToLoadEmotions": "Falha ao carregar emoções", "tts-document": "Arquivo para Voz", "assignVoicesToSpeakers": "Atribuir Vozes aos Falantes", "speakers": "Alto-falantes", "addSpeaker": "<PERSON><PERSON><PERSON><PERSON>", "noVoiceAssigned": "Sem voz atribuída", "noSpeakersAdded": "Nenhum palestrante adicionado ainda", "assignVoiceToSpeaker": "Atribuir voz a {speaker}", "assigned": "Atribu<PERSON><PERSON>", "assign": "Atribuir", "editSpeaker": "<PERSON><PERSON>", "speakerName": "Nome do Palestrante", "enterSpeakerName": "Digite o nome do orador", "save": "<PERSON><PERSON>", "speaker": "Alto-falante", "assignVoices": "Atribuir <PERSON>", "speakersWithVoices": "{assigned}/{total} locutores têm vozes", "dialogs": "Diálogos", "addDialog": "<PERSON><PERSON><PERSON><PERSON>", "enterDialogText": "Digite o texto do diálogo...", "selectSpeaker": "Selecionar Orador", "generateDialogSpeech": "<PERSON><PERSON><PERSON>", "voice 1": "Voz 1", "voice 2": "Voz 2", "uuid": "UUID", "output_format": "Formato de Saída", "output_channel": "Canal de Saída", "file_name": "Nome do Arquivo", "file_size": "Tamanho do Arquivo", "speakers_count": "Contagem de Alto-falantes", "custom_prompt": "Prompt Personalizado", "Please wait a moment...": "Por favor, aguarde um momento...", "Click to copy": "Clique para copiar", "Copied to clipboard": "Copiado para a área de transferência", "UUID has been copied to clipboard": "UUID foi copiado para a área de transferência", "Credits: {credits} remaining": "Créditos: {credits} restantes", "This generation will cost: {cost} Credits": "Esta geração custará: {cost} Créditos", "Your generated video will appear here": "Seu vídeo gerado aparecerá aqui", "Regenerate Video": "Regenerar <PERSON>", "You haven't made any changes to the settings. Are you sure you want to regenerate the same video?": "Você não fez nenhuma alteração nas configurações. Tem certeza de que deseja regenerar o mesmo vídeo?", "Your generated speech will appear here": "Sua fala gerada aparecerá aqui.", "Regenerate Speech": "Regenerar fala", "You haven't made any changes to the settings. Are you sure you want to regenerate the same speech?": "Você não fez nenhuma alteração nas configurações. Tem certeza de que deseja regenerar o mesmo discurso?", "Generated Speech": "Discurso G<PERSON>do", "Generating speech...": "Gerando fala...", "View Details": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "Speech Examples": "Exemplos de Discursos", "Click on any example to use its prompt for speech generation": "Clique em qualquer exemplo para usar seu prompt para geração de fala.", "Click to use": "Clique para usar", "videoStyles.selectVideoStyle": "Selecionar Estilo de Vídeo", "videoStyles.cinematic": "Cinematográfico", "videoStyles.realistic": "Realista", "videoStyles.animated": "Animado", "videoStyles.artistic": "Artístico", "videoStyles.documentary": "Documentário", "videoStyles.vintage": "Vintage", "ui.buttons.downloadApp": "Baixar App", "ui.buttons.signUp": "Inscrever-se", "ui.buttons.viewDetails": "<PERSON><PERSON>", "ui.buttons.seeLater": "<PERSON><PERSON>", "ui.buttons.selectFile": "Selecionar arquivo", "ui.buttons.selectFiles": "Selecionar arquivos", "ui.buttons.pickAVoice": "Escolha uma voz", "ui.buttons.topUpNow": "Recar<PERSON><PERSON> agora", "ui.buttons.pressEscToClose": "Pressione ESC para fechar", "ui.labels.clickToCopy": "Clique para copiar", "ui.labels.copiedToClipboard": "Copiado para a área de transferência", "ui.labels.noAudioAvailable": "Áudio não disponível.", "ui.labels.noThumbnailAvailable": "Nenhuma miniatura disponível", "ui.labels.noPromptAvailable": "Nenhum prompt dispon<PERSON><PERSON>.", "ui.labels.videoModel": "<PERSON><PERSON> de Vídeo", "ui.labels.speechModel": "<PERSON><PERSON>", "ui.labels.generatedSpeech": "Fala Gerada", "ui.labels.defaultVoice": "<PERSON><PERSON>", "ui.labels.selectAnyVoice": "Selecione qualquer voz", "ui.labels.cameraMotion": "Movimento da câmera", "ui.labels.transform": "Transformar", "ui.labels.transforming": "Transformando...", "ui.messages.imageLoaded": "<PERSON><PERSON>", "ui.messages.imageRevealComplete": "Imagem revelada completamente", "ui.messages.processingImage": "Processando imagem", "ui.messages.videoLoaded": "<PERSON><PERSON><PERSON><PERSON>", "ui.messages.videoProcessing": "Processamento de vídeo", "ui.messages.invalidDownloadLink": "Link de download inválido", "ui.messages.pleaseSelectSupportedFile": "Por favor, selecione um arquivo suportado", "ui.messages.deleteConfirm": "Confirmar exclusão", "ui.messages.deleteFailed": "Falha ao excluir", "ui.messages.youHaveNewNotification": "Você tem uma nova notificação", "ui.messages.yourGenerationIsReady": "Sua geração está pronta", "ui.errors.errorLoadingImage": "Erro ao carregar imagem:", "ui.errors.failedToCopy": "Falha ao copiar: ", "ui.errors.failedToPlayAudioPreview": "Falha ao reproduzir a pré-visualização de áudio:", "ui.errors.wavesurferError": "<PERSON><PERSON>:", "ui.errors.somethingWentWrong": "Algo deu errado. Por favor, tente novamente.", "ui.errors.supabaseUrlRequired": "URL do Supabase e chave anônima são necessárias", "dialog.startTypingHere": "Comece a digitar o diálogo aqui...", "payment.debitCreditCard": "Cartão de Débito ou Crédito", "payment.cardDescription": "Visa, Mastercard, American Express e mais", "Style Description": "Descrição do Estilo", "Dialog Content": "Conteúdo do Diálogo", "Your generated dialog will appear here": "Seu diálogo gerado aparecerá aqui.", "Regenerate Dialog": "Redefinir <PERSON>", "Generated Dialog": "Diálogo Gerado", "Generating dialog...": "Gerando diálogo...", "Dialog Information": "Informações de Diálogo", "Audio Player": "Reproduto<PERSON> <PERSON>", "Voices": "Vozes", "Voice 1": "Voz 1", "Voice 2": "Voz 2", "Dialog Examples": "Exemplos de Diálogo", "Click on any example to use its style or dialog content": "Clique em qualquer exemplo para usar seu estilo ou conteúdo de diálogo.", "Use Style": "Use estilo", "Use Dialog": "Usar Diálogo", "personGeneration": "Geração de Pessoas", "Imagen": "Imagem", "On": "Em", "Off": "Des<PERSON><PERSON>", "Prompts will always be refined to improve output quality": "Os prompts serão sempre refinados para melhorar a qualidade dos resultados.", "Prompts will not be modified": "Os prompts não serão modificados", "Tips": "Dicas", "Your video is still being generated in the background. You can close this page and check the history tab for the generated video and we will notify you when it is ready.": "Seu vídeo ainda está sendo gerado em segundo plano. Você pode fechar esta página e verificar a aba de histórico para o vídeo gerado, e nós o notificaremos quando estiver pronto.", "Go to History": "<PERSON>r para Histórico", "footer.youtube": "Youtube", "footer.doctransGPT": "DoctransGPT", "footer.textToSpeechOpenAI": "Texto para Fala OpenAI", "footer.privacyPolicy": "Política de Privacidade", "footer.termsOfService": "Termos de Serviço", "footer.terms": "Termos", "footer.privacy": "Privacidade", "Generate": "<PERSON><PERSON><PERSON>", "Prompt": "Comand<PERSON>", "Generate Video": "<PERSON><PERSON><PERSON>", "ui.errors.generationFailed": "Geração falhou", "downloadVideo": "Baixar Vídeo", "imageStyles.selectImageStyle": "Selecionar Estilo de Imagem", "imageStyles.none.description": "Nenhum estilo específico aplicado", "imageStyles.3d-render.description": "Renderizar imagem em 3D", "imageStyles.acrylic.description": "Crie uma imagem no estilo de pintura acrílica", "imageStyles.anime-general.description": "Gerar imagem em estilo anime", "imageStyles.creative.description": "Aplicar efeitos artísticos criativos", "imageStyles.dynamic.description": "Crie visuais dinâmicos e energéticos", "imageStyles.fashion.description": "Imagem de estilo para fotografia de moda", "imageStyles.game-concept.description": "Imagem de design para conceito de arte de jogo", "imageStyles.graphic-design-3d.description": "Aplique elementos de design gráfico 3D", "imageStyles.illustration.description": "Crie ilustrações artísticas", "imageStyles.portrait.description": "Otimize para fotografia de retrato", "imageStyles.portrait-cinematic.description": "Criar estilo de retrato cinematográfico", "imageStyles.portrait-fashion.description": "Aplicar estilo de retrato de moda", "imageStyles.ray-traced.description": "Renderizar com efeitos de ray tracing", "imageStyles.stock-photo.description": "Criar estilo de foto de banco de imagens profissional", "imageStyles.watercolor.description": "Aplicar efeitos de pintura em aquarela", "imageStyles.examples": "Exemplos", "ui.messages.dragDropOrClick": "Arraste e solte arquivos aqui ou clique para selecionar", "ui.messages.dropFilesHere": "Solte os arquivos aqui", "ui.messages.selectMultipleFiles": "Você pode selecionar vários arquivos.", "ui.messages.selectSingleFile": "Selecione um arquivo para enviar", "ui.messages.supportedFormats": "Formatos suportados", "ui.messages.releaseToUpload": "Liberar para enviar", "ui.labels.generatedAudio": "<PERSON><PERSON><PERSON>", "ui.actions.showResult": "<PERSON><PERSON>", "ui.actions.hideResult": "O<PERSON>lta<PERSON>", "ui.messages.speechGenerating": "Gerando fala...", "Your speech is still being generated in the background. You can close this page and check the history tab for the generated audio and we will notify you when it is ready.": "Seu discurso ainda está sendo gerado em segundo plano. Você pode fechar esta página e verificar a guia de histórico para o áudio gerado e nós o notificaremos quando estiver pronto.", "downloadAudio": "<PERSON><PERSON><PERSON>", "All Countries": "Todos os países", "All Genders": "Todos os gêneros", "Country": "<PERSON><PERSON>", "Gender": "<PERSON><PERSON><PERSON><PERSON>", "Reset": "Redefinir", "Search by name or description...": "Pesquisar por nome ou descrição...", "Male": "<PERSON><PERSON><PERSON><PERSON>", "Female": "Feminino", "American": "Americano", "British": "Britânico", "Australian": "<PERSON><PERSON>", "Indian": "Indiano", "Chinese": "<PERSON><PERSON><PERSON>", "Spanish": "Espanhol", "Canadian": "Canadense", "Irish": "<PERSON><PERSON><PERSON><PERSON>", "Singaporean": "Singapurense", "Russian": "<PERSON>", "German": "Alemão", "Portuguese": "Português", "Hindi": "Hindi", "Mexican": "Mexicano", "Latin American": "Latino-americano", "Argentine": "Argentino", "Peninsular": "Peninsular", "French": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Parisian": "Parisiense", "Standard": "Padrão", "Brazilian": "Brasileiro", "Turkish": "<PERSON><PERSON><PERSON>", "Istanbul": "Istambul", "Bavarian": "<PERSON><PERSON><PERSON>", "Polish": "<PERSON><PERSON><PERSON><PERSON>", "Italian": "Italiano", "South African": "Sul-africano", "Scottish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Welsh": "<PERSON><PERSON><PERSON><PERSON>", "New Zealand": "Nova Zelândia", "Dutch": "<PERSON><PERSON><PERSON><PERSON>", "Belgian": "Belga", "Swedish": "Sueco", "Norwegian": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Danish": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Korean": "<PERSON><PERSON>", "Korean, Seoul": "Coreano, Seul", "Japanese": "<PERSON><PERSON><PERSON><PERSON>", "Croatian": "Croata", "Czech": "Tcheco", "Moravian": "Moraviano", "Zealandic": "Zelandês", "Indonesian": "Indonésio", "Javanese": "<PERSON><PERSON><PERSON><PERSON>", "Romanian": "Rome<PERSON>", "Swiss": "Suíço", "Vietnamese": "Vietnamita", "Arabic": "<PERSON><PERSON><PERSON>", "Bulgarian": "Búlgaro", "Finnish": "<PERSON><PERSON><PERSON>", "Greek": "<PERSON><PERSON>", "Hungarian": "<PERSON><PERSON><PERSON><PERSON>", "Filipino": "Filipino", "History": "História", "imagen-flash": "Gemini 2.0 Flash", "Detail": "<PERSON><PERSON><PERSON>", "Delete": "Excluir", "ui.errors.unknownError": "Ocorreu um erro desconhecido.", "ui.errors.tryAgainLater": "Por favor, tente novamente mais tarde.", "More": "<PERSON><PERSON>", "tts-text": "<PERSON><PERSON><PERSON>", "tts-multi-speaker": "<PERSON><PERSON><PERSON>", "tts-history": "<PERSON><PERSON><PERSON>", "tts-history_1": "<PERSON><PERSON><PERSON>", "tts-history_2": "<PERSON><PERSON><PERSON>", "tts-history_3": "<PERSON><PERSON><PERSON>", "voice-training": "Treinamento de Voz", "voice-training_1": "Treinamento Vocal", "voice-training_2": "Treinamento Vocal", "Start writing or paste your text here or select a file to generate speech...": "Comece a escrever ou cole seu texto aqui ou selecione um arquivo para gerar fala...", "Selecting a voice...": "Selecionando uma voz...", "Voices Library": "Biblioteca de Vozes", "Select a voice for your speaker from the library.": "Selecione uma voz para seu alto-falante na biblioteca.", "Next": "Próximo", "Back": "Voltar", "Done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "I got it!": "Entendi!", "historyPages.endOfHistory": "Você chegou ao fim da história.", "Press ESC to close": "Pressione ESC para fechar", "Your generated image will appear here": "Sua imagem gerada aparecerá aqui", "Generate Speech": "<PERSON><PERSON><PERSON> fala", "Start writing or paste your text here to generate speech...": "Comece a escrever ou cole seu texto aqui para gerar fala...", "Video Gen": "Vídeo Gen", "Generate videos from text prompts and images.": "Gerar vídeos a partir de prompts de texto e imagens.", "Speech Gen": "Geração de Discurso", "Convert text and documents to natural speech.": "Converter texto e documentos em fala natural.", "Dialogue Gen": "Geração de Diálogo", "Create natural conversations with multiple speakers.": "Crie conversas naturais com vários falantes.", "Veo 2": "Veo 2", "Text to Video": "Texto para Vídeo", "Image to Video": "Imagem para Vídeo", "Up to 8 seconds": "Até 8 segundos", "1080p Quality": "Qualidade 1080p", "Multiple Styles": "<PERSON><PERSON><PERSON>s Estilos", "Text to Speech": "Texto para Fala", "Document to Speech": "Documento para Fala", "Multi-Speaker Support": "Suporte a vários palestrantes", "50+ Voices": "50+ Vozes", "Multiple Languages": "<PERSON><PERSON><PERSON><PERSON>", "Emotion Control": "Controle Emocional", "Multi-Speaker Dialogue": "Diálogo Multiusuário", "Natural Conversations": "Conversas Naturais", "Voice Customization": "Customização de Voz", "Emotion Expression": "Expressão Emocional", "Script Generation": "Geração de Script", "Audio Export": "Exportação de Áudio", "Home": "Casa", "Price per 1 character: {cost} Credits": "Preço por 1 caractere: {cost} Créditos", "veo-2": "Veo 2", "veo-3": "Veo 3", "Your speech generation is still being generated in the background. You can close this page and check the history tab for the generated speech and we will notify you when it is ready.": "A geração do seu discurso ainda está sendo processada em segundo plano. Você pode fechar esta página e verificar a aba de histórico para o discurso gerado, e nós o notificaremos quando estiver pronto.", "Create Another": "<PERSON><PERSON><PERSON>", "estimated_credit": "<PERSON><PERSON><PERSON><PERSON>", "tts-flash": "Gemini 2.5 Flash", "Select Another Voice": "Selecionar Outra Voz", "Custom prompt {count}": "Prompt personalizado {count}", "Custom prompt": "Prompt personalizado", "Prompt name": "Nome do prompt", "This name will help you identify your prompt.": "Este nome ajudará você a identificar seu prompt.", "Save as new": "Salvar como novo", "Ok, save it!": "Ok, salve isso!", "Don't use": "Não use", "Use": "<PERSON>ar", "You have the right to use the speech output generated by our services for personal, educational, or commercial purposes.": "Você tem o direito de usar a síntese de voz gerada por nossos serviços para fins pessoais, educacionais ou comerciais.", "Your saved prompts": "Seus prompts salvos", "Success": "Sucesso", "Saved prompt successfully": "Prompt salvo com sucesso", "Error": "Erro", "Saved prompt failed": "Falha ao salvar o prompt", "Updated prompt successfully": "Prompt atualizado com sucesso", "Updated prompt failed": "Solicitação atualizada falhou", "Are you sure you want to delete this prompt?": "Tem certeza de que deseja excluir este prompt?", "Deleted prompt successfully": "Prompt excluído com sucesso", "Deleted prompt failed": "Prompt deletado falhou", "Enter your custom prompt here.": "Insira seu comando personalizado aqui.", "Ex: Funny prompt": "Ex: Solicitação engraçada", "Discard": "Descar<PERSON>", "Update": "<PERSON><PERSON><PERSON><PERSON>", "Edit": "<PERSON><PERSON>", "Custom Prompt": "Prompt Personalizado", "Your credits will never expire.": "Seus créditos nunca expirarão.", "Available credits": "Créditos disponíveis", "{n}+ Styles": "{n}+ <PERSON><PERSON><PERSON><PERSON>", "Create images from text prompts.": "Criar imagens a partir de sugestões de texto.", "/Image": "/Imagem", "/Video": "/<PERSON><PERSON><PERSON><PERSON>", "/1 character": "/1 caractere", "Buy credits": "<PERSON>mp<PERSON>", "My Account": "<PERSON><PERSON>", "Manage your account, credits, and orders.": "Gerencie sua conta, créditos e pedidos.", "Full Name": "Nome <PERSON>to", "Total Available Credits": "<PERSON><PERSON><PERSON><PERSON> Disponíve<PERSON>", "Locked Credits": "Créditos Bloqueados", "Save changes": "<PERSON><PERSON>", "Your account has been updated.": "Sua conta foi atualizada.", "This field is required.": "Este campo é obrigatório.", "User Info": "Informações do Usuário", "Email": "E-mail", "Used to sign in, for email receipts and product updates.": "Usado para fazer login, receber recibos por e-mail e atualizações de produtos.", "Active and valid credits only": "Créditos ativos e válidos apenas", "We lock your credits to perform transactions.": "Bloqueamos seus créditos para realizar transações.", "Referral Link": "Link de Indicação", "Share your referral link to earn credits.": "Compartilhe seu link de indicação para ganhar créditos.", "Referral Code": "Código de Referência", "Your Referral Code": "Seu Código de Referência", "Copy": "Copiar", "Copied!": "Copiado!", "Orders": "Pedidos", "Manage your orders.": "<PERSON><PERSON><PERSON><PERSON> seus pedidos.", "Will appear on receipts, invoices, and other communication.": "Aparecerá em recibos, faturas e outras comunicações.", "User Information": "Informações do Usuário", "Must be at least 8 characters": "Deve ter pelo menos 8 caracteres", "Passwords must be different": "<PERSON><PERSON> devem ser diferentes.", "Passwords must match": "As senhas devem coincidir.", "Current password": "<PERSON><PERSON> atual", "New password": "Nova senha", "Confirm new password": "Confirme a nova senha", "Password": "<PERSON><PERSON>", "Confirm your current password before setting a new one.": "Confirme sua senha atual antes de definir uma nova.", "Account": "Conta", "No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.": "Não quer mais usar nosso serviço? Você pode excluir sua conta aqui. Esta ação é irreversível. Todas as informações relacionadas a esta conta serão excluídas permanentemente.", "Delete account": "Excluir conta", "Change Password": "<PERSON><PERSON><PERSON><PERSON>", "Security": "Segurança", "Credit Statistics": "Estatísticas de Crédito", "enhance_prompt": "Apr<PERSON><PERSON> Prompt", "Current Plan": "Plano Atual", "When you buy credits, you will be upgraded to Premium Plan.": "Ao comprar crédit<PERSON>, você será atualizado para o Plano Premium.", "Available Credits": "Créditos Disponíveis", "Purchased Credits": "Créditos Comprados", "Plan Credits": "Créditos do Plano", "profile.passwordChanged": "Senha Alterada", "profile.passwordChangedDescription": "Sua senha foi alterada com sucesso.", "profile.passwordChangeError": "Falha na Alteração de Senha", "profile.passwordChangeErrorDescription": "Houve um erro ao alterar sua senha. Por favor, tente novamente.", "delete": "Excluir", "profile.deleteAccount": "Excluir Conta", "profile.deleteAccountConfirmation": "Tem certeza de que deseja excluir sua conta? Esta ação não pode ser desfeita e todos os seus dados serão perdidos permanentemente.", "profile.accountDeleted": "Conta excluída", "profile.accountDeletedDescription": "Sua conta foi excluída com sucesso.", "profile.accountDeletionError": "Falha na Exclusão da Conta", "profile.accountDeletionErrorDescription": "Houve um erro ao excluir sua conta. Por favor, tente novamente.", "To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.": "Para comemorar nosso lançamento, aproveite 50% de desconto em modelos selecionados da API Gemini. Oferta válida até novo aviso.", "Check now": "Verifique agora", "payment.success.title": "Pagamento bem-sucedido!", "payment.success.message": "Obrigado pela sua compra! Seu pagamento foi processado com sucesso.", "payment.success.orderId": "ID do Pedido:", "payment.success.redirecting": "Redirecionando para seus pedidos em {seconds} segundos...", "payment.success.viewOrders": "Ver meus pedidos", "payment.error.title": "Erro <PERSON>", "payment.error.message": "Houve um problema ao processar seu pagamento. Por favor, entre em contato com o suporte se isso continuar.", "payment.error.backToOrders": "Voltar aos Pedidos", "Overview of your credits status.": "Visão geral do status dos seus créditos.", "Payment History": "Históric<PERSON> de Pagamentos", "Your payment history will appear here once you have made a purchase.": "Seu histórico de pagamentos aparecerá aqui assim que você fizer uma compra.", "Payment method": "Forma de pagamento", "Purchase Date": "Data de Compra", "Amount": "Quantidade", "Status": "Status", "Payment amount": "Valor do pagamento", "payment.status.unavailable": "Indisponível", "payment.status.created": "<PERSON><PERSON><PERSON>", "payment.status.completed": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "payment.status.failed": "Fal<PERSON>", "payment.status.canceled": "Cancelado", "payment.status.processing": "Processamento", "payment.status.refund": "Reembolso", "payment.status.partial_paid": "Parcialmente <PERSON>", "apiKeys.successTitle": "Chave de API criada com sucesso", "apiKeys.importantNotice": "Aviso Importante", "apiKeys.copyWarning": "Esta é a única vez que você poderá visualizar e copiar esta chave de API. Por favor, copie-a agora e armazene-a de forma segura.", "apiKeys.key": "Chave de <PERSON>", "apiKeys.securityTip": "Dicas de Segurança:", "apiKeys.tip1": "Armazene esta chave em um local seguro.", "apiKeys.tip2": "Nunca compartilhe sua chave de API publicamente.", "apiKeys.tip3": "Se comprometida, exclua esta chave e crie uma nova.", "apiKeys.copyFirst": "Copiar chave de API primeiro", "common.done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Integration": "Integração", "API Keys": "<PERSON>ves de <PERSON>", "Manage your API keys.": "Gerencie suas chaves de API.", "Create API Key": "<PERSON><PERSON><PERSON><PERSON>", "Name your API key.": "Dê um nome à sua chave de API.", "Create": "<PERSON><PERSON><PERSON>", "You have not created any API keys yet.": "Você ainda não criou nenhuma chave de API.", "Copy API Key": "Copiar chave API", "Delete API Key": "Excluir chave de API", "EMAIL_NOT_EXIST": "Email não existe", "Your account is not verified": "Sua conta não está verificada.", "Your account is not verified. Please verify your account to continue": "Sua conta não está verificada. Por favor, verifique sua conta para continuar.", "TOKEN_USED": "Token já utilizado", "NOT_ENOUGH_CREDIT": "Crédito insuficiente. Por favor, recarregue sua conta.", "Not enough credit": "Crédito insuficiente", "Your account does not have enough credit. Please top up your account to continue.": "Sua conta não possui crédito suficiente. Por favor, recarregue sua conta para continuar.", "{n} credits": "{n} c<PERSON><PERSON><PERSON>", "USD / {unit}": "USD / {unidade}", "Credits / {unit}": "Créditos / {unit}", "Save {n}%": "Economize {n}%", "${price} = {n} credits": "${price} = {n} créditos", "You can switch between money and credits to see the price in your preferred currency.": "Você pode alternar entre dinheiro e créditos para ver o preço na sua moeda preferida.", "Forever": "Para sempre", "For large organizations.": "Para grandes organizações.", "Free": "<PERSON><PERSON><PERSON><PERSON>", "Contact us": "Contacte-nos", "Contact sales": "<PERSON><PERSON><PERSON> vendas", "Premium": "Premium", "Enterprise": "Empresa", "Show money": "<PERSON><PERSON>", "Show credits": "<PERSON>rar c<PERSON>", "Auto upgrade after buy credits": "Atualização automática após comprar créditos", "Image": "Imagem", "Video": "Vídeo", "Audio": "<PERSON><PERSON><PERSON>", "Dialog": "Diálogo", "Get started": "Comece", "Contact": "Contato", "Image Style": "<PERSON><PERSON><PERSON>", "Image Aspect Ratio": "Proporção de Imagem", "Enhance Prompt": "<PERSON><PERSON><PERSON> Prompt", "Aspect Ratio": "Proporção de Aspecto", "Support multiple aspect ratio": "Suporte a múltiplas proporções de aspecto", "Support enhance prompt": "Aprimorar suporte rá<PERSON>o", "Up to {size}MB": "Até {size}MB", "Budget Calculator": "Calculadora de Orçamento", "Resource Calculator": "Calculadora de Recursos", "Budget Amount": "Valor do Orçamento", "Resources you can generate:": "Recursos que você pode gerar:", "Select resources you want:": "Selecione os recursos que deseja:", "credits": "c<PERSON><PERSON><PERSON>", "image": "imagem", "video": "vídeo", "per item": "por item", "Quantity": "Quantidade", "Total Cost:": "Custo Total:", "Approximately {credits} credits": "Aproximadamente {credits} créditos", "Images": "Imagens", "Videos": "Vídeos", "Pricing Calculator": "Calculadora de Preços", "Calculate how many resources can you generate with your budget.": "Calcule quantos recursos você pode gerar com seu orçamento.", "Minimum $10 required": "Mínimo de $10 necessário", "Minimum Purchase Required": "Compra Mínima Exigida", "Minimum purchase amount is $10. Please increase your selection.": "O valor mínimo de compra é de $10. Por favor, aumente sua seleção.", "Minimum purchase amount is $10": "Quantidade mínima de compra é $10", "Please add more resources to reach the minimum purchase amount.": "Por favor, adicione mais recursos para atingir o valor mínimo de compra.", "Enter exact number": "Digite o número exato", "Enter budget amount": "Insira o valor do orçamento", "Min: $10": "Mín: $10", "Each amount shows what you can generate with your entire budget (choose one type)": "Cada valor mostra o que você pode gerar com todo o seu orçamento (escolha um tipo)", "OR": "OU", "AI Image Generation Examples": "Exemplos de Geração de Imagens por IA", "Explore the power of AI image generation with these interactive comparisons": "Explore o poder da geração de imagens por IA com essas comparações interativas.", "Try the Comparison!": "Experimente a Comparação!", "Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.": "Arraste o controle deslizante para a esquerda e para a direita para comparar as imagens de antes e depois. Você também pode clicar em qualquer lugar na imagem para mover o controle deslizante.", "Got it!": "Entendi!", "Please login to access your saved prompts.": "Faça login para acessar seus prompts salvos.", "Access Your Personal Voices": "Acesse Suas Voz<PERSON>", "Access Your Favorite Voices": "Acesse suas vozes favoritas", "Login to view and manage your personal voice collection. Upload custom voices and access them anytime.": "Faça login para visualizar e gerenciar sua coleção pessoal de vozes. Carregue vozes personalizadas e acesse-as a qualquer momento.", "Login to view your favorite voices. Save voices you love and access them quickly for your projects.": "Faça login para ver suas vozes favoritas. Salve as vozes que você ama e acesse-as rapidamente para seus projetos.", "Create Account": "<PERSON><PERSON><PERSON>", "Join thousands of creators using AI voices for their projects": "Junte-se a milhares de criadores que usam vozes de IA para seus projetos.", "AI Video Generation Examples": "Exemplos de Geração de Vídeo com IA", "Explore the power of AI video generation with these interactive comparisons": "Explore o poder da geração de vídeos por IA com essas comparações interativas.", "Try the Video Comparison!": "Experimente a Comparação de Vídeos!", "Drag the slider left and right to compare text prompts/images with generated videos. You can also click anywhere to move the slider.": "Arraste o controle deslizante para a esquerda e para a direita para comparar prompts de texto/imagens com vídeos gerados. Você também pode clicar em qualquer lugar para mover o controle deslizante.", "Duration": "Duração", "Select video duration in seconds": "Selecionar a duração do vídeo em segundos", "This setting is locked for the selected model": "Esta configuração está bloqueada para o modelo selecionado.", "Prompts will always be refined to improve output quality (required for this model)": "Os prompts serão sempre refinados para melhorar a qualidade do resultado (necessário para este modelo)", "SIGNUP_MAIL_EXIST": "<PERSON><PERSON> j<PERSON>e", "SIGNIN_USER_NOT_FOUND": "Usuário não encontrado", "SIGNIN_USER_NOT_VERIFIED": "Usuário não verificado", "SIGNIN_USER_DISABLED": "<PERSON><PERSON><PERSON><PERSON>", "SIGNIN_WRONG_PASSWORD": "Senha incorreta", "SIGNIN_USER_NOT_FOUND_FOR_EMAIL": "Usuário não encontrado para o email", "SIGNIN_INVALID_EMAIL": "<PERSON><PERSON>", "auth.accountCreated": "<PERSON>ta criada", "auth.accountCreatedDescription": "Sua conta foi criada com sucesso. Por favor, verifique seu e-mail para verificar sua conta.", "Select voice on right": "Selecionar voz à direita", "privacy.lastUpdated": "Última atualização:", "privacy.lastUpdatedDate": "15 de janeiro de 2025", "privacy.introduction": "Na GeminiGen.AI, priorizamos a proteção da sua privacidade e a segurança das suas informações pessoais. Esta política de privacidade descreve como coletamos, utilizamos e protegemos as informações que você fornece ao usar nossos serviços de geração de conteúdo por IA, incluindo geração de imagens, geração de vídeos, síntese de fala e geração de diálogos. Ao acessar e usar nosso site (geminigen.ai), você consente com as práticas descritas nesta política.", "privacy.informationCollectionDescription": "Quando você cria uma conta em nosso site, coletamos certas informações pessoais, como seu endereço de e-mail e nome completo. Essas informações são necessárias para conceder acesso aos nossos serviços, fornecer atualizações ou alterações nos nossos serviços e para análise estatística visando melhorar nossas ofertas. Além disso, qualquer texto, imagem ou documento enviado para geração de conteúdo por IA é armazenado temporariamente apenas para o propósito de gerar o resultado.", "privacy.creditCalculation": "C<PERSON>l<PERSON>lo de Crédito", "privacy.creditCalculationDescription": "Para garantir uma faturação precisa, o número de créditos necessários para a geração de conteúdo de IA é calculado com base no texto, imagens ou documentos fornecidos. Esse cálculo é realizado usando nosso algoritmo proprietário e é diretamente proporcional à complexidade e extensão da entrada.", "privacy.paymentSecurity": "3. Pagamento e Segurança", "privacy.paymentSecurityDescription": "Para o processamento de pagamentos, oferecemos opções de PayPal e cartão de crédito. Não armazenamos informações de cartão de crédito em nossos servidores. Todas as transações de pagamento são tratadas de forma segura por provedores de serviços de pagamento terceiros confiáveis, em conformidade com suas respectivas políticas de privacidade e segurança.", "privacy.emailNotification": "4. Notificação por Email e Acesso ao Conteúdo Gerado", "privacy.emailNotificationDescription": "Ao concluir a geração de conteúdo, você receberá uma notificação por e-mail contendo um link seguro para acessar e baixar o material gerado (imagens, vídeos, arquivos de áudio ou diálogos). Este link permanecerá ativo por um período especificado para sua conveniência.", "privacy.thirdPartyServices": "6. Serviços de Terceiros", "privacy.thirdPartyServicesDescription": "Podemos utilizar serviços de terceiros, como provedores de análises, para aprimorar nossos serviços e analisar padrões de uso. Esses serviços podem coletar informações sobre seu uso, mas não têm acesso às suas informações pessoais.", "privacy.cookies": "7. Cookies e Tecnologias de Rastreamento", "privacy.cookiesDescription": "Nosso site utiliza cookies e tecnologias de rastreamento similares para melhorar a experiência do usuário e analisar o uso do site. Você tem a opção de desativar os cookies nas configurações do seu navegador, mas esteja ciente de que alguns recursos do nosso site podem não funcionar corretamente como resultado.", "privacy.thirdPartyLinks": "8. <PERSON><PERSON> Terceiros", "privacy.thirdPartyLinksDescription": "Nosso site pode conter links para sites de terceiros. Não somos responsáveis pelas práticas de privacidade ou pelo conteúdo desses sites e incentivamos você a revisar suas respectivas políticas de privacidade.", "privacy.childrenPrivacy": "9. Privacidade das Crianças", "privacy.childrenPrivacyDescription": "Nossos serviços não são destinados a indivíduos menores de 18 anos, e não coletamos ou armazenamos, conscientemente, informações pessoais de ninguém com menos dessa idade. Se tomarmos conhecimento da coleta inadvertida de informações pessoais de uma criança com menos de 18 anos, tomaremos medidas para remover essas informações de nossos registros.", "privacy.policyChanges": "10. Atualizações em nossa Política de Privacidade", "privacy.policyChangesDescription": "Podemos atualizar periodicamente nossa Política de Privacidade para refletir mudanças nas práticas ou requisitos legais. Quaisquer revisões entrarão em vigor imediatamente após a publicação da política atualizada em nosso site. Recomendamos que você revise esta Política de Privacidade periodicamente para obter as informações mais recentes.", "privacy.commercialUse": "11. <PERSON><PERSON>", "privacy.commercialUseDescription": "Você tem o direito de usar o conteúdo gerado por nossos serviços para fins pessoais, educacionais ou comerciais. No entanto, você não pode revender, redistribuir ou sublicenciar o conteúdo gerado sem o consentimento prévio por escrito da GeminiGen.AI.", "privacy.otherPeoplePrivacy": "12. Privacidade de outras pessoas", "privacy.otherPeoplePrivacyDescription": "Você deve respeitar a privacidade dos outros ao usar nossos serviços. Não carregue ou crie conteúdo que contenha informações pessoais, dados confidenciais ou material protegido por direitos autorais sem permissão.", "privacy.unsubscribe": "13. <PERSON><PERSON><PERSON> <PERSON>s<PERSON><PERSON><PERSON>", "privacy.unsubscribeDescription": "Você pode desativar a publicidade direcionada clicando no botão 'Alternar' nas configurações do seu perfil.", "terms.lastUpdated": "Última atualização:", "terms.lastUpdatedDate": "15 de janeiro de 2025", "terms.introduction": "Bem-vindo ao GeminiGen.AI. Estes Termos de Serviço regem o seu uso dos nossos serviços de geração de conteúdo com tecnologia de IA, incluindo geração de imagens, geração de vídeos, síntese de fala e geração de diálogos.", "terms.acceptanceOfTermsDetails": "Seu uso contínuo de nossos serviços constitui aceitação de quaisquer alterações a estes Termos.", "terms.serviceDescription": "2. Descrição dos Serviços", "terms.serviceDescriptionText": "A GeminiGen.AI oferece serviços de geração de conteúdo com tecnologia de IA, incluindo, mas não se limitando a:", "terms.serviceUsageDescription": "Você se compromete a empregar nossos serviços exclusivamente para fins legais. Você deve abster-se de carregar, transmitir ou armazenar qualquer conteúdo que seja ilegal, prejudicial, difamatório ou que infrinja os direitos de terceiros. Você é o único responsável por qualquer conteúdo submetido para geração de conteúdo por IA.", "terms.permittedUse": "Uso Permitido 4.1", "terms.permitted1": "Utilize nossos serviços para fins legais, criativos e comerciais.", "terms.permitted2": "G<PERSON><PERSON> conteúdo que esteja em conformidade com as leis e regulamentações aplicáveis.", "terms.permitted3": "Respeite os direitos de propriedade intelectual dos outros", "terms.permitted4": "Utilize conteúdo gerado de acordo com nossos termos de licença.", "terms.prohibitedUse": "4.2 Uso Proibido", "terms.prohibited1": "<PERSON><PERSON><PERSON> conteúdo que seja ilegal, prejudicial, ameaçador, abusivo ou discriminatório", "terms.prohibited2": "<PERSON><PERSON><PERSON> conteúdo que viole os direitos de propriedade intelectual de terceiros", "terms.prohibited3": "<PERSON>du<PERSON><PERSON> conteúdo destinado a enganar, fraudar ou iludir outras pessoas.", "terms.prohibited4": "<PERSON><PERSON><PERSON> conteúdo retratando menores em situações inadequadas.", "terms.prohibited5": "<PERSON><PERSON><PERSON> conteúdo que promova violência, terrorismo ou atividades ilegais.", "terms.prohibited6": "Use nossos serviços para enviar spam, assediar ou prejudicar outros", "terms.prohibited7": "Tentar fazer engenharia reversa, hackear ou comprometer nossos sistemas", "terms.prohibited8": "Violar quaisquer leis ou regulamentos aplicáveis", "terms.userAccounts1": "Fornecer informações de registro precisas e completas", "terms.userAccounts2": "Manter a segurança e a confidencialidade das credenciais da sua conta", "terms.userAccounts3": "<PERSON><PERSON> as atividades que ocorrem na sua conta", "terms.userAccounts4": "Notificar-nos imediatamente sobre qualquer uso não autorizado da sua conta.", "terms.userAccounts5": "G<PERSON><PERSON><PERSON> que as informações da sua conta permaneçam atuais e precisas", "terms.paymentAndBilling": "3. Pagamento e Créditos", "terms.paymentAndBillingDescription": "Nossos serviços de geração de conteúdo por IA operam em um sistema baseado em créditos. O número de créditos necessário para a criação de conteúdo é determinado por nosso algoritmo proprietário e é calculado com precisão com base na complexidade da entrada e nos requisitos de saída.", "terms.payment1": "Ao esgotamento do seu saldo de crédito, você deve recarregar sua conta.", "terms.payment2": "Os pagamentos podem ser feitos via PayPal ou cartão de crédito.", "terms.payment3": "Todos os pagamentos são processados de forma segura por meio de processadores de pagamento de terceiros.", "terms.payment4": "Créditos não são reembolsáveis, exceto conforme exigido pela legislação aplicável.", "terms.payment5": "Os preços estão sujeitos a alterações com aviso prévio razoável.", "terms.payment6": "Você é responsável por todos os impostos e taxas aplicáveis.", "terms.ourIntellectualProperty": "5.1 Nossa Propriedade Intelectual", "terms.ourIntellectualPropertyDescription": "GeminiGen.AI e seus serviços, incluindo todo o software, algoritmos, designs e conteúdo, estão protegidos por leis de propriedade intelectual. Você não pode copiar, modificar, distribuir ou criar trabalhos derivados sem nossa permissão expressa por escrito.", "terms.userGeneratedContent": "Conteúdo Gerado pelo <PERSON>uá<PERSON> 5.2", "terms.userGeneratedContentDescription": "Você mantém a propriedade do conteúdo que cria usando nossos serviços, sujeito ao seguinte:", "terms.userContent1": "Você nos concede uma licença limitada para processar e armazenar seu conteúdo para fornecer nossos serviços.", "terms.userContent2": "Você declara que tem o direito de usar qualquer conteúdo fornecido por você.", "terms.userContent3": "Você é responsável por garantir que seu conteúdo gerado esteja em conformidade com estes Termos.", "terms.userContent4": "Podemos remover conteúdo que viole nossas políticas ou leis aplicáveis.", "terms.privacyAndDataProtection": "7. Privacidade e Proteção de Dados", "terms.privacyAndDataProtectionDescription": "Sua privacidade é importante para nós. Nossa coleta, uso e proteção de suas informações pessoais são regidos por nossa Política de Privacidade, que é incorporada a estes Termos por referência.", "terms.serviceAvailability": "8. Disponibilidade do Serviço", "terms.serviceAvailabilityDescription": "Embora nos esforcemos para fornecer serviços confiáveis, não garantimos acesso ininterrupto. Nossos serviços podem estar temporariamente indisponíveis devido a manutenção, atualizações ou problemas técnicos. Reservamo-nos o direito de modificar ou descontinuar serviços com aviso prévio razoável.", "terms.terminationByUser": "8.1 Rescisão por Você", "terms.terminationByUserDescription": "Você pode encerrar sua conta a qualquer momento, entrando em contato com nossa equipe de suporte. Após o encerramento, seu acesso aos nossos serviços será interrompido, mas estes Termos continuarão a se aplicar ao seu uso anterior de nossos serviços.", "terms.terminationByUs": "8.2 Rescisão por Nós", "terms.terminationByUsDescription": "Podemos suspender ou encerrar sua conta e o acesso aos nossos serviços imediatamente, com ou sem aviso prévio, por qualquer um dos seguintes motivos:", "terms.termination1": "Violação destes Termos de Serviço", "terms.termination2": "Atividade fraudulenta, abusiva ou ilegal", "terms.termination3": "Não pagamento de taxas ou encargos", "terms.termination4": "Períodos prolongados de inatividade", "terms.termination5": "Requisitos legais ou regulamentares", "terms.termination6": "Proteção dos nossos direitos, propriedade ou segurança", "terms.limitationOfLiability": "6. Limitação de Responsabilidade", "terms.limitationOfLiabilityDescription": "A GeminiGen.AI não será responsabilizada por quaisquer danos diretos, indiretos, incidentais, especiais ou consequenciais resultantes do uso dos nossos serviços ou relacionados a ele. Não garantimos a precisão, integridade ou disponibilidade dos serviços e isentamo-nos de todas as garantias, expressas ou implícitas, em relação ao seu uso ou resultados.", "terms.disclaimer1": "Garantias de comercialização, adequação para um propósito específico e não violação", "terms.disclaimer2": "Garantias quanto à precisão, confiabilidade ou qualidade do conteúdo gerado.", "terms.disclaimer3": "Responsabilidade pelo uso ou distribuição do conteúdo gerado", "terms.disclaimer4": "Responsabilidade por quaisquer danos resultantes de interrupções de serviço ou problemas técnicos", "terms.indemnification": "12. Indenização", "terms.indemnificationDescription": "Você concorda em indenizar, defender e isentar a GeminiGen.AI e suas afiliadas de quaisquer reivindicações, danos, perdas ou despesas decorrentes do seu uso de nossos serviços, violação destes Termos ou violação de direitos de terceiros.", "terms.governingLaw": "9. <PERSON><PERSON>", "terms.governingLawDescription": "Estes Termos de Serviço devem ser interpretados e regidos de acordo com as leis do Vietnã, sem consideração aos seus princípios de conflito de leis. Quaisquer disputas decorrentes ou relacionadas a estes Termos e à utilização dos nossos serviços estarão sujeitas à jurisdição exclusiva dos tribunais no Vietnã.", "terms.clarificationOpenAI": "10. Esclarecimento sobre Serviços de IA de Terceiros", "terms.clarificationOpenAIDescription": "GeminiGen.AI é uma entidade independente e não é afiliada à OpenAI, Google ou outros fornecedores de serviços de IA. Nossos serviços de geração de conteúdo utilizam várias APIs de IA para converter texto em imagens, vídeos, fala e diálogo, mas operamos de forma independente desses fornecedores. Esta explicação é feita para evitar qualquer confusão ou mal-entendido sobre a relação entre a GeminiGen.AI e fornecedores de serviços de IA de terceiros. Os usuários devem estar cientes de que, embora usemos tecnologia de IA de terceiros para fornecer nossos serviços, a GeminiGen.AI é a única responsável pela operação de nossos serviços e pelo cumprimento destes Termos de Serviço.", "terms.contactEmail": "Email:", "terms.contactAddress": "Endereço:", "terms.companyAddress": "Endereço da Organização GeminiGen.AI: 3100 BOWDOIN ST DES MOINES, IA 50313 Estados Unidos", "second": "segundo", "1M characters": "1M caracteres", "Support {n}+ voices": "Suporte a {n}+ vozes", "Support {n}+ emotions": "Apoiar {n}+ em<PERSON><PERSON><PERSON><PERSON>", "Support {n}+ languages": "Suporte a {n}+ idiomas", "Support custom prompt": "A<PERSON><PERSON> prompt personalizado", "Support MP3 and WAV": "Suporte a MP3 e WAV", "Support speed control": "Controle de velocidade de suporte", "Support document to speech": "Documento de apoio ao discurso", "Current plan": "Plano atual", "Already premium": "Já é premium"}